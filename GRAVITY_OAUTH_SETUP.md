# GravityWrite OAuth Registration Flow Setup

This document explains how to set up and use the custom GravityWrite OAuth registration flow.

## Overview

The GravityWrite OAuth system allows the application to act as both OAuth client and server, enabling SSO flows where the application redirects to its own OAuth authorization endpoint with custom parameters like `is_register` and `email`.

## Configuration

### 1. Environment Variables

Add the following to your `.env` file:

```env
# GravityWrite Self-Referential OAuth Configuration
GW_CLIENT_ID=your-gravity-client-id
GW_CLIENT_SECRET=your-gravity-client-secret
GW_REDIRECT_URI=https://your-domain.com/oauth/gravity/callback
```

### 2. Create OAuth Client

Create an OAuth client in your database for the gravity provider:

```php
use App\Models\Passport\Client;

$client = Client::create([
    'id' => 'gravity-sso-client',
    'name' => 'GravityWrite SSO Client',
    'secret' => 'your-client-secret',
    'redirect' => 'https://your-domain.com/oauth/gravity/callback',
    'personal_access_client' => false,
    'password_client' => false,
    'revoked' => false,
]);
```

## Usage

### Basic Registration Flow

```php
use Laravel\Socialite\Facades\Socialite;

// Redirect to registration flow with email
return Socialite::driver('gravity')
    ->with([
        'is_register' => true,
        'email' => '<EMAIL>'
    ])
    ->redirect();
```

### Registration Flow Scenarios

#### 1. New Email Registration
```php
// User will be redirected to registration page with pre-filled email
Socialite::driver('gravity')
    ->with(['is_register' => true, 'email' => '<EMAIL>'])
    ->redirect();
```

#### 2. Existing Email Registration
```php
// User will be redirected to login page with pre-filled email
Socialite::driver('gravity')
    ->with(['is_register' => true, 'email' => '<EMAIL>'])
    ->redirect();
```

#### 3. Gmail Registration
```php
// User will be redirected to Google OAuth for Gmail addresses
Socialite::driver('gravity')
    ->with(['is_register' => true, 'email' => '<EMAIL>'])
    ->redirect();
```

#### 4. Registration Without Email
```php
// User will be redirected to registration page
Socialite::driver('gravity')
    ->with(['is_register' => true])
    ->redirect();
```

## Flow Logic

### OAuth Authorization Flow

1. **Request to `/oauth/authorize`** with `is_register=1` parameter
2. **User Logout**: If user is logged in and `is_register=1`, logout current user
3. **Session Setup**: Initialize OAuth session with flow type and parameters
4. **Redirect to Login**: Redirect to login page with OAuth parameters preserved

### Login Controller Logic

1. **Parameter Check**: Check for `is_register=1` and `email` parameters
2. **Email Validation**: Check if email exists in database
3. **Flow Decision**:
   - **New Email**: Redirect to registration with pre-filled email
   - **Existing Email**: Show login form with pre-filled email
   - **Gmail Address**: Redirect to Google OAuth
   - **No Email**: Redirect to registration page

### Post-Authentication

After successful login/registration:
1. **OAuth Completion**: Complete OAuth authorization flow
2. **Authorization Code**: Generate and return authorization code
3. **Client Redirect**: Redirect to client application with authorization code

## Testing

Run the comprehensive tests to verify the OAuth flow:

```bash
# Run specific OAuth registration flow tests
php artisan test tests/Feature/Auth/GravityOAuthRegistrationFlowTest.php

# Run all SSO tests
./run_comprehensive_sso_tests.sh
```

## Security Considerations

1. **Client Secret**: Always use secure client secrets in production
2. **HTTPS**: Use HTTPS for all OAuth redirects in production
3. **Session Security**: Sessions are regenerated during OAuth flows for security
4. **Email Verification**: Users must verify their email before OAuth completion

## Troubleshooting

### Common Issues

1. **Invalid Client Error**: Ensure `GW_CLIENT_ID` matches your OAuth client ID
2. **Redirect URI Mismatch**: Ensure `GW_REDIRECT_URI` matches your client's redirect URI
3. **Session Issues**: Clear browser cache and cookies if experiencing session problems

### Debug Mode

Enable debug logging to troubleshoot OAuth flows:

```env
LOG_LEVEL=debug
```

Check logs in `storage/logs/laravel.log` for detailed OAuth flow information.

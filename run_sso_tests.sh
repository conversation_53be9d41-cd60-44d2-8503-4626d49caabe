#!/bin/bash

# Comprehensive Laravel Passport SSO Integration Tests Runner
# This script runs all the SSO-related tests and provides a summary

echo "🚀 Running Comprehensive Laravel Passport SSO Integration Tests"
echo "=============================================================="

# Change to the project directory
cd /Applications/XAMPP/xamppfiles/htdocs/gravitywrite/gravitywrite-auth

echo ""
echo "📋 Test Categories:"
echo "1. Database Isolation Tests"
echo "2. Wildcard Redirect URI Tests"
echo "3. Enhanced Registration Flow Tests (with SSO)"
echo "4. Enhanced Password Reset Flow Tests (with SSO)"
echo "5. Enhanced Email Verification Flow Tests (with SSO)"
echo ""

# Run Database Isolation Tests
echo "🔍 Running Database Isolation Tests..."
php artisan test --testsuite=Feature --filter="DatabaseIsolationTest" --stop-on-failure
if [ $? -eq 0 ]; then
    echo "✅ Database Isolation Tests: PASSED"
else
    echo "❌ Database Isolation Tests: FAILED"
    exit 1
fi

echo ""

# Run Wildcard Redirect URI Tests
echo "🌐 Running Wildcard Redirect URI Tests..."
php artisan test --testsuite=Feature --filter="WildcardRedirectTest" --stop-on-failure
if [ $? -eq 0 ]; then
    echo "✅ Wildcard Redirect URI Tests: PASSED"
else
    echo "❌ Wildcard Redirect URI Tests: FAILED"
    exit 1
fi

echo ""

# Run Enhanced Registration Flow Tests
echo "📝 Running Enhanced Registration Flow Tests..."
php artisan test --testsuite=Feature --filter="RegistrationFlowTest" --stop-on-failure
if [ $? -eq 0 ]; then
    echo "✅ Enhanced Registration Flow Tests: PASSED"
else
    echo "❌ Enhanced Registration Flow Tests: FAILED"
    exit 1
fi

echo ""

# Run Enhanced Password Reset Flow Tests
echo "🔐 Running Enhanced Password Reset Flow Tests..."
php artisan test --testsuite=Feature --filter="PasswordResetFlowTest" --stop-on-failure
if [ $? -eq 0 ]; then
    echo "✅ Enhanced Password Reset Flow Tests: PASSED"
else
    echo "❌ Enhanced Password Reset Flow Tests: FAILED"
    exit 1
fi

echo ""

# Run Enhanced Email Verification Flow Tests
echo "📧 Running Enhanced Email Verification Flow Tests..."
php artisan test --testsuite=Feature --filter="EmailVerificationFlowTest" --stop-on-failure
if [ $? -eq 0 ]; then
    echo "✅ Enhanced Email Verification Flow Tests: PASSED"
else
    echo "❌ Enhanced Email Verification Flow Tests: FAILED"
    exit 1
fi

echo ""

# Run Session Management Tests
echo "🔄 Running Session Management Tests..."
php artisan test --testsuite=Feature --filter="SessionManagementTest" --stop-on-failure
if [ $? -eq 0 ]; then
    echo "✅ Session Management Tests: PASSED"
else
    echo "❌ Session Management Tests: FAILED"
    exit 1
fi

echo ""
echo "🎉 ALL COMPREHENSIVE SSO INTEGRATION TESTS PASSED!"
echo "=============================================================="
echo ""
echo "📊 Test Summary:"
echo "✅ Database Isolation: SQLite test database properly isolated from MySQL development database"
echo "✅ Wildcard Redirect URIs: *.ngrok-free.app patterns working correctly"
echo "✅ SSO Registration Flow: is_register=1 parameter handling, user logout, email validation"
echo "✅ SSO Password Reset Flow: OAuth context preservation, post-reset redirects"
echo "✅ SSO Email Verification Flow: Mandatory verification, OAuth context preservation"
echo "✅ Session Management: OAuth session data preservation across authentication flows"
echo ""
echo "🔒 Security Features Tested:"
echo "• Client secret verification in OAuth flows"
echo "• Email verification enforcement"
echo "• Session isolation and validation"
echo "• Wildcard redirect URI security"
echo ""
echo "🏗️ Infrastructure Verified:"
echo "• SQLite in-memory test database isolation"
echo "• MySQL development database preservation"
echo "• Factory patterns for test data creation"
echo "• Comprehensive assertion coverage"
echo ""
echo "Ready for production deployment! 🚀"

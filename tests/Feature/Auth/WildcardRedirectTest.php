<?php

namespace Tests\Feature\Auth;

use App\Models\Passport\Client;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class WildcardRedirectTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Verify database isolation
        $this->assertUsingTestDatabase();
        
        // Create test user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
            'verified' => 1,
        ]);
    }

    public function test_database_isolation_for_wildcard_tests()
    {
        $this->assertUsingTestDatabase();
        $this->assertDevelopmentDatabasePreserved();
        
        // Verify clean state
        $this->assertEquals(1, User::count());
    }

    public function test_wildcard_ngrok_redirect_uri_validation()
    {
        $this->assertUsingTestDatabase();
        
        // Create client with wildcard ngrok redirect
        $client = Client::factory()->create([
            'id' => 'ngrok-wildcard-client',
            'redirect' => 'https://*.ngrok-free.app/oauth/callback',
        ]);
        
        // Test various valid ngrok subdomains
        $validRedirects = [
            'https://abc123.ngrok-free.app/oauth/callback',
            'https://test-app.ngrok-free.app/oauth/callback',
            'https://my-app-123.ngrok-free.app/oauth/callback',
            'https://dev-environment.ngrok-free.app/oauth/callback',
            'https://staging-server.ngrok-free.app/oauth/callback',
        ];
        
        foreach ($validRedirects as $redirectUri) {
            $response = $this->actingAs($this->user)->get('/oauth/authorize?' . http_build_query([
                'client_id' => $client->id,
                'redirect_uri' => $redirectUri,
                'response_type' => 'code',
                'scope' => '',
            ]));
            
            // Should not fail due to redirect URI validation
            $this->assertTrue(
                $response->isSuccessful() || $response->isRedirection(),
                "Failed for redirect URI: {$redirectUri}"
            );
        }
    }

    public function test_wildcard_redirect_uri_rejects_invalid_domains()
    {
        $this->assertUsingTestDatabase();
        
        // Create client with wildcard ngrok redirect
        $client = Client::factory()->create([
            'id' => 'ngrok-wildcard-client',
            'redirect' => 'https://*.ngrok-free.app/oauth/callback',
        ]);
        
        // Test invalid domains that should be rejected
        $invalidRedirects = [
            'https://malicious.com/oauth/callback',
            'https://evil.ngrok-free.app.malicious.com/oauth/callback',
            'https://ngrok-free.app.evil.com/oauth/callback',
            'http://test.ngrok-free.app/oauth/callback', // Wrong protocol
            'https://test.ngrok.com/oauth/callback', // Wrong domain
        ];
        
        foreach ($invalidRedirects as $redirectUri) {
            $response = $this->actingAs($this->user)->get('/oauth/authorize?' . http_build_query([
                'client_id' => $client->id,
                'redirect_uri' => $redirectUri,
                'response_type' => 'code',
                'scope' => '',
            ]));
            
            // Should fail or redirect to error for invalid redirect URIs
            $this->assertTrue(
                $response->status() >= 400 || 
                ($response->isRedirection() && str_contains($response->headers->get('Location'), 'error')),
                "Should have failed for invalid redirect URI: {$redirectUri}"
            );
        }
    }

    public function test_multiple_wildcard_patterns_support()
    {
        $this->assertUsingTestDatabase();
        
        // Create client with multiple wildcard patterns
        $client = Client::factory()->create([
            'id' => 'multi-wildcard-client',
            'redirect' => 'https://*.ngrok-free.app/oauth/callback,https://*.test.com/oauth/callback',
        ]);
        
        // Test both wildcard patterns
        $testRedirects = [
            'https://app1.ngrok-free.app/oauth/callback',
            'https://app2.test.com/oauth/callback',
            'https://dev.test.com/oauth/callback',
            'https://staging.ngrok-free.app/oauth/callback',
        ];
        
        foreach ($testRedirects as $redirectUri) {
            $response = $this->actingAs($this->user)->get('/oauth/authorize?' . http_build_query([
                'client_id' => $client->id,
                'redirect_uri' => $redirectUri,
                'response_type' => 'code',
                'scope' => '',
            ]));
            
            // Should work for both patterns
            $this->assertTrue(
                $response->isSuccessful() || $response->isRedirection(),
                "Failed for redirect URI: {$redirectUri}"
            );
        }
    }

    public function test_wildcard_with_specific_paths()
    {
        $this->assertUsingTestDatabase();
        
        // Create client with wildcard that includes specific path
        $client = Client::factory()->create([
            'id' => 'path-wildcard-client',
            'redirect' => 'https://*.ngrok-free.app/auth/oauth/callback',
        ]);
        
        // Test with correct path
        $response = $this->actingAs($this->user)->get('/oauth/authorize?' . http_build_query([
            'client_id' => $client->id,
            'redirect_uri' => 'https://test.ngrok-free.app/auth/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
        ]));
        
        $this->assertTrue($response->isSuccessful() || $response->isRedirection());
        
        // Test with incorrect path
        $response = $this->actingAs($this->user)->get('/oauth/authorize?' . http_build_query([
            'client_id' => $client->id,
            'redirect_uri' => 'https://test.ngrok-free.app/wrong/path',
            'response_type' => 'code',
            'scope' => '',
        ]));
        
        // Should fail for wrong path
        $this->assertTrue($response->status() >= 400 || 
            ($response->isRedirection() && str_contains($response->headers->get('Location'), 'error')));
    }

    public function test_wildcard_subdomain_depth_validation()
    {
        $this->assertUsingTestDatabase();
        
        // Create client with wildcard
        $client = Client::factory()->create([
            'id' => 'depth-wildcard-client',
            'redirect' => 'https://*.ngrok-free.app/oauth/callback',
        ]);
        
        // Test various subdomain depths
        $testRedirects = [
            'https://simple.ngrok-free.app/oauth/callback',
            'https://multi-word.ngrok-free.app/oauth/callback',
            'https://with-numbers-123.ngrok-free.app/oauth/callback',
            'https://very-long-subdomain-name-here.ngrok-free.app/oauth/callback',
        ];
        
        foreach ($testRedirects as $redirectUri) {
            $response = $this->actingAs($this->user)->get('/oauth/authorize?' . http_build_query([
                'client_id' => $client->id,
                'redirect_uri' => $redirectUri,
                'response_type' => 'code',
                'scope' => '',
            ]));
            
            $this->assertTrue(
                $response->isSuccessful() || $response->isRedirection(),
                "Failed for redirect URI: {$redirectUri}"
            );
        }
    }

    public function test_wildcard_case_sensitivity()
    {
        $this->assertUsingTestDatabase();
        
        // Create client with lowercase wildcard
        $client = Client::factory()->create([
            'id' => 'case-wildcard-client',
            'redirect' => 'https://*.ngrok-free.app/oauth/callback',
        ]);
        
        // Test case variations
        $testRedirects = [
            'https://test.ngrok-free.app/oauth/callback',
            'https://TEST.ngrok-free.app/oauth/callback',
            'https://Test.ngrok-free.app/oauth/callback',
            'https://tEsT.ngrok-free.app/oauth/callback',
        ];
        
        foreach ($testRedirects as $redirectUri) {
            $response = $this->actingAs($this->user)->get('/oauth/authorize?' . http_build_query([
                'client_id' => $client->id,
                'redirect_uri' => $redirectUri,
                'response_type' => 'code',
                'scope' => '',
            ]));
            
            // Domain names are case-insensitive, so all should work
            $this->assertTrue(
                $response->isSuccessful() || $response->isRedirection(),
                "Failed for redirect URI: {$redirectUri}"
            );
        }
    }

    public function test_wildcard_with_query_parameters()
    {
        $this->assertUsingTestDatabase();
        
        // Create client with wildcard
        $client = Client::factory()->create([
            'id' => 'query-wildcard-client',
            'redirect' => 'https://*.ngrok-free.app/oauth/callback',
        ]);
        
        // Test with additional query parameters
        $response = $this->actingAs($this->user)->get('/oauth/authorize?' . http_build_query([
            'client_id' => $client->id,
            'redirect_uri' => 'https://test.ngrok-free.app/oauth/callback?state=xyz&extra=param',
            'response_type' => 'code',
            'scope' => '',
        ]));
        
        $this->assertTrue($response->isSuccessful() || $response->isRedirection());
    }

    public function test_non_wildcard_client_strict_validation()
    {
        $this->assertUsingTestDatabase();
        
        // Create client with specific (non-wildcard) redirect
        $client = Client::factory()->create([
            'id' => 'strict-client',
            'redirect' => 'https://specific.ngrok-free.app/oauth/callback',
        ]);
        
        // Test with exact match (should work)
        $response = $this->actingAs($this->user)->get('/oauth/authorize?' . http_build_query([
            'client_id' => $client->id,
            'redirect_uri' => 'https://specific.ngrok-free.app/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
        ]));
        
        $this->assertTrue($response->isSuccessful() || $response->isRedirection());
        
        // Test with different subdomain (should fail)
        $response = $this->actingAs($this->user)->get('/oauth/authorize?' . http_build_query([
            'client_id' => $client->id,
            'redirect_uri' => 'https://different.ngrok-free.app/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
        ]));
        
        // Should fail for non-matching redirect URI
        $this->assertTrue($response->status() >= 400 || 
            ($response->isRedirection() && str_contains($response->headers->get('Location'), 'error')));
    }

    protected function tearDown(): void
    {
        // Verify database isolation is maintained
        $this->assertUsingTestDatabase();
        
        parent::tearDown();
    }
}

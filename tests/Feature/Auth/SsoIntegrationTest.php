<?php

namespace Tests\Feature\Auth;

use App\Models\Passport\Client;
use App\Models\Passport\OAuthSession;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

class SsoIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected Client $oauthClient;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Verify database isolation
        $this->assertUsingTestDatabase();
        
        // Create test OAuth client
        $this->oauthClient = Client::create([
            'id' => 'sso-test-client',
            'name' => 'SSO Test Client',
            'secret' => 'sso-test-secret',
            'redirect' => 'http://sso.test.com/oauth/callback',
            'personal_access_client' => false,
            'password_client' => false,
            'revoked' => false,
        ]);
        
        // Create test user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
            'verified' => 1,
        ]);
    }

    public function test_database_isolation_is_maintained()
    {
        $this->assertUsingTestDatabase();
        $this->assertDevelopmentDatabasePreserved();
        
        // Verify we start with clean state
        $this->assertEquals(1, User::count());
        $this->assertEquals(1, Client::count());
        $this->assertEquals(0, OAuthSession::count());
    }

    public function test_sso_registration_flow_with_is_register_parameter()
    {
        $this->assertUsingTestDatabase();
        
        // Create a logged-in user who should be logged out
        /** @var User $existingUser */
        $existingUser = User::factory()->create([
            'email' => '<EMAIL>',
            'verified' => 1,
        ]);

        $this->actingAs($existingUser);
        $this->assertTrue(Auth::check());
        
        // Set up OAuth parameters with is_register=1
        $oauthParams = [
            'client_id' => $this->oauthClient->id,
            'redirect_uri' => 'http://sso.test.com/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => '<EMAIL>',
        ];
        
        // Access OAuth authorization with registration parameters
        $response = $this->get('/oauth/authorize?' . http_build_query($oauthParams));
        
        // Should redirect to login (user should be logged out)
        $response->assertRedirect('/login');
        
        // Verify user was logged out due to is_register=1
        $this->assertFalse(Auth::check());
        
        // Verify OAuth session data is preserved
        $this->assertTrue(Session::has('redirect_uri'));
        $this->assertEquals('registration', Session::get('oauth_flow_type'));
    }

    public function test_sso_registration_email_existence_validation()
    {
        $this->assertUsingTestDatabase();
        
        // Set up OAuth parameters with existing email
        $oauthParams = [
            'client_id' => $this->oauthClient->id,
            'redirect_uri' => 'http://sso.test.com/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => $this->user->email, // Existing email
        ];
        
        $response = $this->get('/oauth/authorize?' . http_build_query($oauthParams));
        
        // Should redirect to login page
        $response->assertRedirect('/login');
        
        // Follow redirect to login
        $response = $this->get('/login');
        
        // Should redirect to registration with error about existing email
        $response->assertRedirect();
        
        // Verify session contains registration flow data
        $this->assertEquals('registration', Session::get('oauth_flow_type'));
    }

    public function test_sso_standard_login_flow()
    {
        $this->assertUsingTestDatabase();
        
        // Set up OAuth parameters for login
        $oauthParams = [
            'client_id' => $this->oauthClient->id,
            'redirect_uri' => 'http://sso.test.com/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
        ];
        
        // Access OAuth authorization
        $response = $this->get('/oauth/authorize?' . http_build_query($oauthParams));
        
        // Should redirect to login
        $response->assertRedirect('/login');
        
        // Verify OAuth session data is preserved
        $this->assertTrue(Session::has('redirect_uri'));
        $this->assertEquals('login', Session::get('oauth_flow_type'));
        
        // Perform login
        $response = $this->post('/login', [
            'email' => $this->user->email,
            'password' => 'password123',
        ]);
        
        // Should redirect to OAuth authorization
        $response->assertRedirect();
        $this->assertAuthenticatedAs($this->user);
    }

    public function test_oauth_authorization_and_token_exchange()
    {
        $this->assertUsingTestDatabase();
        
        // Set up OAuth session
        Session::put('redirect_uri', '/oauth/authorize?' . http_build_query([
            'client_id' => $this->oauthClient->id,
            'redirect_uri' => 'http://sso.test.com/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
        ]));
        
        // Authenticate user and access authorization
        $response = $this->actingAs($this->user)->get('/oauth/authorize?' . http_build_query([
            'client_id' => $this->oauthClient->id,
            'redirect_uri' => 'http://sso.test.com/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
        ]));
        
        // Should show authorization page (or auto-approve)
        $this->assertTrue($response->isSuccessful() || $response->isRedirection());
        
        if ($response->isRedirection()) {
            // Auto-approved, should redirect with authorization code
            $location = $response->headers->get('Location');
            $this->assertStringContainsString('code=', $location);
        }
    }

    public function test_session_management_and_token_handling()
    {
        $this->assertUsingTestDatabase();
        
        // Create OAuth session
        $oauthSession = OAuthSession::factory()->forUser($this->user)->create();
        
        // Verify session was created
        $this->assertDatabaseHas('oauth_sessions', [
            'user_id' => $this->user->id,
            'code' => $oauthSession->code,
        ]);
        
        // Test session cleanup
        $this->user->delete();
        
        // OAuth session should be cleaned up via foreign key constraint
        $this->assertDatabaseMissing('oauth_sessions', [
            'user_id' => $this->user->id,
        ]);
    }

    public function test_wildcard_redirect_uri_functionality()
    {
        $this->assertUsingTestDatabase();
        
        // Create client with wildcard redirect
        $wildcardClient = Client::factory()->withWildcardRedirect()->create([
            'id' => 'wildcard-client',
        ]);
        
        // Test various ngrok subdomains
        $testRedirects = [
            'https://abc123.ngrok-free.app/oauth/callback',
            'https://test-app.ngrok-free.app/oauth/callback',
            'https://my-app-123.ngrok-free.app/oauth/callback',
        ];
        
        foreach ($testRedirects as $redirectUri) {
            $oauthParams = [
                'client_id' => $wildcardClient->id,
                'redirect_uri' => $redirectUri,
                'response_type' => 'code',
                'scope' => '',
            ];
            
            $response = $this->actingAs($this->user)->get('/oauth/authorize?' . http_build_query($oauthParams));
            
            // Should not fail due to redirect URI validation
            $this->assertTrue($response->isSuccessful() || $response->isRedirection());
        }
    }

    public function test_mandatory_email_verification_enforcement()
    {
        $this->assertUsingTestDatabase();
        
        // Create unverified user
        /** @var User $unverifiedUser */
        $unverifiedUser = User::factory()->create([
            'email_verified_at' => null,
            'verified' => 0,
        ]);

        // Try to access OAuth authorization
        $response = $this->actingAs($unverifiedUser)->get('/oauth/authorize?' . http_build_query([
            'client_id' => $this->oauthClient->id,
            'redirect_uri' => 'http://sso.test.com/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
        ]));
        
        // Should redirect to email verification
        $response->assertRedirect();
        $this->assertStringContainsString('verify', $response->headers->get('Location'));
    }

    public function test_client_secret_verification_in_oauth_flows()
    {
        $this->assertUsingTestDatabase();
        
        // Test token exchange with correct client secret
        $response = $this->post('/oauth/token', [
            'grant_type' => 'authorization_code',
            'client_id' => $this->oauthClient->id,
            'client_secret' => $this->oauthClient->secret,
            'redirect_uri' => 'http://sso.test.com/oauth/callback',
            'code' => 'test-auth-code',
        ]);
        
        // Should not fail due to client authentication (may fail for other reasons)
        $this->assertNotEquals(401, $response->status());
        
        // Test with incorrect client secret
        $response = $this->post('/oauth/token', [
            'grant_type' => 'authorization_code',
            'client_id' => $this->oauthClient->id,
            'client_secret' => 'wrong-secret',
            'redirect_uri' => 'http://sso.test.com/oauth/callback',
            'code' => 'test-auth-code',
        ]);
        
        // Should fail with client authentication error
        $this->assertEquals(401, $response->status());
    }

    protected function tearDown(): void
    {
        // Verify database isolation is maintained after each test
        $this->assertUsingTestDatabase();
        
        parent::tearDown();
    }
}

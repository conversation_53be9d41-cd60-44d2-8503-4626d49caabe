<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

class LoginFlowTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Assert we're using the test database for every test
        $this->assertUsingTestDatabase();
    }

    public function test_database_isolation_is_working()
    {
        // Verify we're using SQLite in-memory database
        $this->assertUsingTestDatabase();
        
        // Verify development database configuration is preserved
        $this->assertDevelopmentDatabasePreserved();
    }

    public function test_user_can_view_login_form()
    {
        $this->assertUsingTestDatabase();
        
        $response = $this->get('/login');
        
        $response->assertStatus(200);
        $response->assertViewIs('auth.login');
        $response->assertSee('Login');
    }

    public function test_user_can_login_with_valid_credentials()
    {
        $this->assertUsingTestDatabase();

        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
            'verified' => 1, // This field is required for login
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertRedirect('/home');
        $this->assertAuthenticatedAs($user);
    }

    public function test_user_cannot_login_with_invalid_credentials()
    {
        $this->assertUsingTestDatabase();
        
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);
        
        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword',
        ]);
        
        $response->assertSessionHasErrors(['email']);
        $this->assertGuest();
    }

    public function test_user_cannot_login_with_unverified_email()
    {
        $this->assertUsingTestDatabase();

        $user = User::factory()->unverified()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'verified' => 0, // Explicitly set as unverified
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        // Should redirect to email verification
        $response->assertRedirect('/email/verify');
        // Note: User is authenticated but redirected due to unverified status
        // This is the expected behavior based on the LoginController logic
    }

    public function test_login_with_oauth_redirect_parameters()
    {
        $this->assertUsingTestDatabase();

        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
            'verified' => 1,
        ]);

        // Set up OAuth session data
        Session::put('redirect_uri', 'http://example.com/oauth/authorize?client_id=123&redirect_uri=http://callback.com&response_type=code');
        Session::put('oauth_flow_type', 'login');

        $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        // Should redirect to OAuth authorization
        $this->assertAuthenticatedAs($user);

        // OAuth session data should be preserved
        $this->assertTrue(Session::has('redirect_uri'));
    }

    public function test_login_with_registration_parameters_redirects_to_registration()
    {
        $this->assertUsingTestDatabase();
        
        $response = $this->get('/login?is_register=1&email=<EMAIL>');
        
        // Should redirect to registration with email parameter
        $response->assertRedirect('/register?email=newuser%40example.com');
    }

    public function test_login_with_existing_email_shows_login_form()
    {
        $this->assertUsingTestDatabase();

        // Create existing user
        User::factory()->create([
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
        ]);

        $response = $this->get('/login?is_register=1&email=<EMAIL>');

        // Should show login form with pre-filled email
        $response->assertStatus(200);
        $response->assertViewIs('auth.login');
        $response->assertViewHas('prefill_email', '<EMAIL>');
    }

    public function test_login_clears_invalid_session_data()
    {
        $this->assertUsingTestDatabase();
        
        // Set up invalid session data
        Session::put('redirect_uri', 'invalid-url-without-oauth-params');
        Session::put('is_first_time', true);
        Session::put('oauth_flow_type', 'registration');
        
        $response = $this->get('/login');
        
        $response->assertStatus(200);
        
        // Invalid session data should be cleared
        $this->assertFalse(Session::has('redirect_uri'));
        $this->assertFalse(Session::has('oauth_flow_type'));
    }

    public function test_authenticated_user_redirected_from_login()
    {
        $this->assertUsingTestDatabase();

        /** @var User $user */
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get('/login');

        $response->assertRedirect('/home');
    }

    public function test_remember_me_functionality()
    {
        $this->assertUsingTestDatabase();

        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
            'verified' => 1,
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'remember' => 'on',
        ]);

        $response->assertRedirect('/home');
        $this->assertAuthenticatedAs($user);

        // Check if remember token is set
        $user->refresh();
        $this->assertNotNull($user->remember_token);
    }

    public function test_login_throttling_after_multiple_failed_attempts()
    {
        $this->assertUsingTestDatabase();

        User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        // Make multiple failed login attempts
        for ($i = 0; $i < 6; $i++) {
            $this->post('/login', [
                'email' => '<EMAIL>',
                'password' => 'wrongpassword',
            ]);
        }

        // Next attempt should be throttled
        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword',
        ]);

        // Should redirect with some kind of error (throttling or validation)
        $response->assertRedirect();
        $this->assertGuest();
    }

    public function test_force_login_functionality()
    {
        $this->assertUsingTestDatabase();

        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'verified' => 1,
        ]);

        $response = $this->post('/force/login', [
            'email' => $user->email, // Force login expects email, not user_id
        ]);

        $this->assertAuthenticatedAs($user);
        // Should redirect to login page as per the controller logic
        $response->assertRedirect('/login');
    }

    public function test_database_isolation_between_login_tests()
    {
        $this->assertUsingTestDatabase();
        
        // This test should start with a clean database
        $this->assertEquals(0, User::count());
        
        // Create a user for this test
        User::factory()->create(['email' => '<EMAIL>']);
        
        $this->assertEquals(1, User::count());
        $this->assertDatabaseHas('users', ['email' => '<EMAIL>']);
    }

    public function test_login_database_isolation_verification()
    {
        $this->assertUsingTestDatabase();
        
        // This test should start with a clean database
        // The user from the previous test should not exist
        $this->assertEquals(0, User::count());
        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);
        
        // Verify we can create a new user without conflicts
        User::factory()->create(['email' => '<EMAIL>']);
        
        $this->assertEquals(1, User::count());
        $this->assertDatabaseHas('users', ['email' => '<EMAIL>']);
    }
}

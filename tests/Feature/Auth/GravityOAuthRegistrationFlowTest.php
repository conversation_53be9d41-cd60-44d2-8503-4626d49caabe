<?php

namespace Tests\Feature\Auth;

use App\Models\Passport\Client;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;
use Tests\TestCase;

/**
 * Test the complete OAuth registration flow using the gravity provider.
 */
class GravityOAuthRegistrationFlowTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Verify database isolation
        $this->assertUsingTestDatabase();
    }

    /**
     * Verify we're using the test database (SQLite in-memory).
     */
    protected function assertUsingTestDatabase(): void
    {
        $connection = config('database.default');
        $database = config("database.connections.{$connection}.database");
        
        $this->assertEquals('sqlite', $connection, 'Tests should use SQLite database');
        $this->assertEquals(':memory:', $database, 'Tests should use in-memory SQLite database');
    }

    public function test_gravity_oauth_registration_flow_with_new_email()
    {
        $this->assertUsingTestDatabase();

        // Create OAuth client for testing
        $client = Client::factory()->create([
            'id' => 'gravity-test-client',
            'redirect' => 'http://test.gravitywrite.com/oauth/callback',
        ]);

        // Set up environment variables for gravity provider
        config([
            'app.gw_client_id' => $client->id,
            'app.gw_client_secret' => $client->secret,
            'app.gw_redirect_uri' => 'http://test.gravitywrite.com/oauth/callback',
        ]);

        // Simulate the OAuth authorization request with registration parameters
        $oauthParams = [
            'client_id' => $client->id,
            'redirect_uri' => 'http://test.gravitywrite.com/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => '<EMAIL>',
        ];

        // Make request to OAuth authorization endpoint
        $response = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Should redirect to login with registration parameters
        $response->assertRedirect();
        $redirectUrl = $response->headers->get('Location');
        
        // Parse the redirect URL to check parameters
        $parsedUrl = parse_url($redirectUrl);
        $this->assertStringContainsString('/login', $parsedUrl['path']);

        parse_str($parsedUrl['query'] ?? '', $queryParams);
        $this->assertEquals('1', $queryParams['is_register']);
        $this->assertEquals('<EMAIL>', $queryParams['email']);
    }

    public function test_gravity_oauth_registration_flow_with_existing_email()
    {
        $this->assertUsingTestDatabase();

        // Create existing user
        $existingUser = User::factory()->create([
            'email' => '<EMAIL>',
            'verified' => 1,
        ]);

        // Create OAuth client for testing
        $client = Client::factory()->create([
            'id' => 'gravity-test-client',
            'redirect' => 'http://test.gravitywrite.com/oauth/callback',
        ]);

        // Set up environment variables for gravity provider
        config([
            'app.gw_client_id' => $client->id,
            'app.gw_client_secret' => $client->secret,
            'app.gw_redirect_uri' => 'http://test.gravitywrite.com/oauth/callback',
        ]);

        // Simulate the OAuth authorization request with existing email
        $oauthParams = [
            'client_id' => $client->id,
            'redirect_uri' => 'http://test.gravitywrite.com/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => '<EMAIL>',
        ];

        // Make request to OAuth authorization endpoint
        $response = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Should redirect to login with registration parameters
        $response->assertRedirect();
        $redirectUrl = $response->headers->get('Location');
        
        // Parse the redirect URL to check parameters
        $parsedUrl = parse_url($redirectUrl);
        $this->assertStringContainsString('/login', $parsedUrl['path']);

        parse_str($parsedUrl['query'] ?? '', $queryParams);
        $this->assertEquals('1', $queryParams['is_register']);
        $this->assertEquals('<EMAIL>', $queryParams['email']);
    }

    public function test_login_controller_handles_registration_flow_correctly()
    {
        $this->assertUsingTestDatabase();

        // Test with new email - should redirect to registration
        $response = $this->get('/login?is_register=1&email=<EMAIL>');
        
        $response->assertRedirect();
        $redirectUrl = $response->headers->get('Location');
        $this->assertStringContainsString('/register', $redirectUrl);

        // Test with existing email - should show login form with prefilled email
        $existingUser = User::factory()->create([
            'email' => '<EMAIL>',
            'verified' => 1,
        ]);

        $response = $this->get('/login?is_register=1&email=<EMAIL>');
        
        $response->assertOk();
        $response->assertViewIs('auth.login');
        $response->assertViewHas('prefill_email', '<EMAIL>');
    }

    public function test_oauth_flow_logs_out_current_user_when_is_register_present()
    {
        $this->assertUsingTestDatabase();

        // Create and login an existing user
        $existingUser = User::factory()->create([
            'email' => '<EMAIL>',
            'verified' => 1,
        ]);

        $this->actingAs($existingUser);
        $this->assertTrue(Auth::check());

        // Create OAuth client
        $client = Client::factory()->create([
            'id' => 'gravity-test-client',
            'redirect' => 'http://test.gravitywrite.com/oauth/callback',
        ]);

        // Set up OAuth parameters with is_register=1
        $oauthParams = [
            'client_id' => $client->id,
            'redirect_uri' => 'http://test.gravitywrite.com/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => '<EMAIL>',
        ];

        // Make request to OAuth authorization endpoint
        $response = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // User should be logged out and redirected to login
        $this->assertFalse(Auth::check());
        $response->assertRedirect();
    }

    public function test_gravity_socialite_provider_is_registered()
    {
        // Test that the gravity provider is properly registered
        $this->assertTrue(class_exists(\Laravel\Socialite\Facades\Socialite::class));

        // This should not throw an exception
        try {
            $driver = Socialite::driver('gravity');
            $this->assertNotNull($driver);
        } catch (\Exception $e) {
            $this->fail('Gravity provider is not registered: ' . $e->getMessage());
        }
    }
}

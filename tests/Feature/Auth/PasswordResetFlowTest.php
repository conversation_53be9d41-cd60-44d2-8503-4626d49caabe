<?php

namespace Tests\Feature\Auth;

use App\Models\Passport\Client;
use App\Models\User;
use App\Notifications\Auth\ResetPassword;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

class PasswordResetFlowTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Assert we're using the test database for every test
        $this->assertUsingTestDatabase();
    }

    public function test_database_isolation_is_working()
    {
        // Verify we're using SQLite in-memory database
        $this->assertUsingTestDatabase();
        
        // Verify development database configuration is preserved
        $this->assertDevelopmentDatabasePreserved();
    }

    public function test_user_can_view_forgot_password_form()
    {
        $this->assertUsingTestDatabase();

        $response = $this->get('/password/reset');

        $response->assertStatus(200);
        $response->assertViewIs('auth.passwords.email');
        // Check for common password reset text instead of exact match
        $response->assertSee('Email');
    }

    public function test_user_can_request_password_reset_with_valid_email()
    {
        $this->assertUsingTestDatabase();
        Notification::fake();
        
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $response = $this->post('/password/email', [
            'email' => '<EMAIL>',
        ]);
        
        $response->assertRedirect();
        $response->assertSessionHas('status');
        
        // Password reset notification should be sent
        Notification::assertSentTo($user, ResetPassword::class);
    }

    public function test_password_reset_fails_with_invalid_email()
    {
        $this->assertUsingTestDatabase();
        Notification::fake();
        
        $response = $this->post('/password/email', [
            'email' => '<EMAIL>',
        ]);
        
        $response->assertSessionHasErrors(['email']);
        
        // No notifications should be sent
        Notification::assertNothingSent();
    }

    public function test_password_reset_fails_with_invalid_email_format()
    {
        $this->assertUsingTestDatabase();
        
        $response = $this->post('/password/email', [
            'email' => 'invalid-email-format',
        ]);
        
        $response->assertSessionHasErrors(['email']);
    }

    public function test_user_can_view_password_reset_form_with_valid_token()
    {
        $this->assertUsingTestDatabase();
        
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $token = Password::createToken($user);

        $response = $this->get("/password/reset/{$token}?email=<EMAIL>");
        
        $response->assertStatus(200);
        $response->assertViewIs('auth.passwords.reset');
        $response->assertViewHas('token', $token);
        $response->assertViewHas('email', '<EMAIL>');
    }

    public function test_user_can_reset_password_with_valid_token()
    {
        $this->assertUsingTestDatabase();
        
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('oldpassword'),
        ]);

        $token = Password::createToken($user);

        $response = $this->post('/password/reset', [
            'token' => $token,
            'email' => '<EMAIL>',
            'password' => 'newpassword123',
            'password_confirmation' => 'newpassword123',
        ]);
        
        $response->assertRedirect('/home');
        $response->assertSessionHas('status');
        
        // User should be authenticated after password reset
        $this->assertAuthenticatedAs($user);
        
        // Password should be updated
        $user->refresh();
        $this->assertTrue(Hash::check('newpassword123', $user->password));
        $this->assertFalse(Hash::check('oldpassword', $user->password));
    }

    public function test_password_reset_fails_with_invalid_token()
    {
        $this->assertUsingTestDatabase();
        
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);
        
        $response = $this->post('/password/reset', [
            'token' => 'invalid-token',
            'email' => '<EMAIL>',
            'password' => 'newpassword123',
            'password_confirmation' => 'newpassword123',
        ]);
        
        $response->assertSessionHasErrors(['email']);
        $this->assertGuest();
        
        // Password should not be changed
        $user->refresh();
        $this->assertFalse(Hash::check('newpassword123', $user->password));
    }

    public function test_password_reset_fails_with_expired_token()
    {
        $this->assertUsingTestDatabase();
        
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);
        
        $token = Password::createToken($user);
        
        // Simulate token expiration by manipulating the database
        $this->travel(61)->minutes();
        
        $response = $this->post('/password/reset', [
            'token' => $token,
            'email' => '<EMAIL>',
            'password' => 'newpassword123',
            'password_confirmation' => 'newpassword123',
        ]);
        
        $response->assertSessionHasErrors(['email']);
        $this->assertGuest();
    }

    public function test_password_reset_fails_with_mismatched_passwords()
    {
        $this->assertUsingTestDatabase();
        
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);
        
        $token = Password::createToken($user);
        
        $response = $this->post('/password/reset', [
            'token' => $token,
            'email' => '<EMAIL>',
            'password' => 'newpassword123',
            'password_confirmation' => 'differentpassword',
        ]);
        
        $response->assertSessionHasErrors(['password']);
        $this->assertGuest();
    }

    public function test_password_reset_fails_with_weak_password()
    {
        $this->assertUsingTestDatabase();
        
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);
        
        $token = Password::createToken($user);
        
        $response = $this->post('/password/reset', [
            'token' => $token,
            'email' => '<EMAIL>',
            'password' => '123', // Too short
            'password_confirmation' => '123',
        ]);
        
        $response->assertSessionHasErrors(['password']);
        $this->assertGuest();
    }

    public function test_password_reset_with_oauth_redirect()
    {
        $this->assertUsingTestDatabase();
        
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('oldpassword'),
        ]);
        
        // Set up OAuth session data
        Session::put('redirect_uri', 'http://example.com/oauth/authorize?client_id=123&redirect_uri=http://callback.com&response_type=code');
        
        $token = Password::createToken($user);
        
        $response = $this->post('/password/reset', [
            'token' => $token,
            'email' => '<EMAIL>',
            'password' => 'newpassword123',
            'password_confirmation' => 'newpassword123',
        ]);
        
        // Should redirect to OAuth URI instead of home
        $response->assertRedirect('http://example.com/oauth/authorize?client_id=123&redirect_uri=http://callback.com&response_type=code');
        
        // User should be authenticated
        $this->assertAuthenticatedAs($user);
        
        // Password should be updated
        $user->refresh();
        $this->assertTrue(Hash::check('newpassword123', $user->password));
    }

    public function test_password_reset_token_is_deleted_after_successful_reset()
    {
        $this->assertUsingTestDatabase();
        
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);
        
        $token = Password::createToken($user);
        
        // Verify token exists
        $this->assertDatabaseHas('password_reset_tokens', [
            'email' => '<EMAIL>',
        ]);
        
        $response = $this->post('/password/reset', [
            'token' => $token,
            'email' => '<EMAIL>',
            'password' => 'newpassword123',
            'password_confirmation' => 'newpassword123',
        ]);
        
        $response->assertRedirect('/home');
        
        // Token should be deleted after successful reset
        $this->assertDatabaseMissing('password_reset_tokens', [
            'email' => '<EMAIL>',
        ]);
    }

    public function test_database_isolation_between_password_reset_tests()
    {
        $this->assertUsingTestDatabase();
        
        // This test should start with a clean database
        $this->assertEquals(0, User::count());
        
        // Create a user for this test
        User::factory()->create(['email' => '<EMAIL>']);
        
        $this->assertEquals(1, User::count());
        $this->assertDatabaseHas('users', ['email' => '<EMAIL>']);
    }

    public function test_password_reset_database_isolation_verification()
    {
        $this->assertUsingTestDatabase();

        // This test should start with a clean database
        // The user from the previous test should not exist
        $this->assertEquals(0, User::count());
        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);

        // Verify we can create a new user without conflicts
        User::factory()->create(['email' => '<EMAIL>']);

        $this->assertEquals(1, User::count());
        $this->assertDatabaseHas('users', ['email' => '<EMAIL>']);
    }

    public function test_sso_password_reset_initiation_via_oauth_flow()
    {
        $this->assertUsingTestDatabase();
        Notification::fake();

        // Create OAuth client
        $client = Client::factory()->create([
            'id' => 'password-reset-client',
            'redirect' => 'http://sso.test.com/oauth/callback',
        ]);

        // Create user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'verified' => 1,
        ]);

        // Set up OAuth session data
        Session::put('redirect_uri', '/oauth/authorize?' . http_build_query([
            'client_id' => $client->id,
            'redirect_uri' => 'http://sso.test.com/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
        ]));
        Session::put('oauth_flow_type', 'login');

        // Request password reset
        $response = $this->post('/password/email', [
            'email' => '<EMAIL>',
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('status');

        // Password reset notification should be sent
        Notification::assertSentTo($user, ResetPassword::class);

        // OAuth session data should be preserved
        $this->assertTrue(Session::has('redirect_uri'));
        $this->assertEquals('login', Session::get('oauth_flow_type'));
    }

    public function test_sso_password_reset_completion_redirects_to_oauth()
    {
        $this->assertUsingTestDatabase();

        // Create OAuth client
        $client = Client::factory()->create([
            'id' => 'reset-complete-client',
            'redirect' => 'http://sso.test.com/oauth/callback',
        ]);

        // Create user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('oldpassword'),
            'verified' => 1,
        ]);

        // Set up OAuth session data
        Session::put('redirect_uri', '/oauth/authorize?' . http_build_query([
            'client_id' => $client->id,
            'redirect_uri' => 'http://sso.test.com/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
        ]));
        Session::put('oauth_flow_type', 'login');

        $token = Password::createToken($user);

        $response = $this->post('/password/reset', [
            'token' => $token,
            'email' => '<EMAIL>',
            'password' => 'newpassword123',
            'password_confirmation' => 'newpassword123',
        ]);

        // Should redirect to OAuth authorization
        $response->assertRedirect();
        $redirectLocation = $response->headers->get('Location');
        $this->assertStringContainsString('/oauth/authorize', $redirectLocation);
        $this->assertStringContainsString($client->id, $redirectLocation);

        // User should be authenticated
        $this->assertAuthenticatedAs($user);

        // Password should be updated
        $user->refresh();
        $this->assertTrue(Hash::check('newpassword123', $user->password));
    }

    public function test_sso_password_reset_with_email_verification_required()
    {
        $this->assertUsingTestDatabase();

        // Create OAuth client
        $client = Client::factory()->create([
            'id' => 'verify-reset-client',
            'redirect' => 'http://sso.test.com/oauth/callback',
        ]);

        // Create unverified user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('oldpassword'),
            'email_verified_at' => null,
            'verified' => 0,
        ]);

        // Set up OAuth session data
        Session::put('redirect_uri', '/oauth/authorize?' . http_build_query([
            'client_id' => $client->id,
            'redirect_uri' => 'http://sso.test.com/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
        ]));

        $token = Password::createToken($user);

        $response = $this->post('/password/reset', [
            'token' => $token,
            'email' => '<EMAIL>',
            'password' => 'newpassword123',
            'password_confirmation' => 'newpassword123',
        ]);

        // Should redirect to email verification instead of OAuth
        $response->assertRedirect();
        $redirectLocation = $response->headers->get('Location');
        $this->assertStringContainsString('verify', $redirectLocation);

        // Password should still be updated
        $user->refresh();
        $this->assertTrue(Hash::check('newpassword123', $user->password));

        // OAuth session should be preserved for post-verification flow
        $this->assertTrue(Session::has('redirect_uri'));
    }

    public function test_sso_password_reset_token_validation_with_oauth_context()
    {
        $this->assertUsingTestDatabase();

        // Create OAuth client
        $client = Client::factory()->create([
            'id' => 'token-validation-client',
            'redirect' => 'http://sso.test.com/oauth/callback',
        ]);

        // Create user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'verified' => 1,
        ]);

        // Set up OAuth session data
        Session::put('redirect_uri', '/oauth/authorize?' . http_build_query([
            'client_id' => $client->id,
            'redirect_uri' => 'http://sso.test.com/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
        ]));

        $token = Password::createToken($user);

        // Test viewing reset form with OAuth context
        $response = $this->get("/password/reset/{$token}?email=<EMAIL>");

        $response->assertStatus(200);
        $response->assertViewIs('auth.passwords.reset');
        $response->assertViewHas('token', $token);
        $response->assertViewHas('email', '<EMAIL>');

        // OAuth session should be preserved
        $this->assertTrue(Session::has('redirect_uri'));
    }

    public function test_sso_password_reset_preserves_oauth_state_across_requests()
    {
        $this->assertUsingTestDatabase();

        // Create OAuth client
        $client = Client::factory()->create([
            'id' => 'state-preservation-client',
            'redirect' => 'http://sso.test.com/oauth/callback',
        ]);

        // Create user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'verified' => 1,
        ]);

        // Set up OAuth session data with state parameter
        $oauthParams = [
            'client_id' => $client->id,
            'redirect_uri' => 'http://sso.test.com/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
            'state' => 'test-state-value',
        ];

        Session::put('redirect_uri', '/oauth/authorize?' . http_build_query($oauthParams));
        Session::put('oauth_flow_type', 'login');
        Session::put('oauth_state', 'test-state-value');

        // Request password reset
        $this->post('/password/email', ['email' => '<EMAIL>']);

        // OAuth state should be preserved
        $this->assertTrue(Session::has('oauth_state'));
        $this->assertEquals('test-state-value', Session::get('oauth_state'));

        // Complete password reset
        $token = Password::createToken($user);
        $response = $this->post('/password/reset', [
            'token' => $token,
            'email' => '<EMAIL>',
            'password' => 'newpassword123',
            'password_confirmation' => 'newpassword123',
        ]);

        // Should redirect to OAuth with preserved state
        $response->assertRedirect();
        $redirectLocation = $response->headers->get('Location');
        $this->assertStringContainsString('state=test-state-value', $redirectLocation);
    }
}

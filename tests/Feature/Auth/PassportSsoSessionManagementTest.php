<?php

namespace Tests\Feature\Auth;

use App\Models\Passport\Client;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class PassportSsoSessionManagementTest extends TestCase
{
    use RefreshDatabase;

    protected Client $defaultClient;
    protected User $existingUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Verify database isolation
        $this->assertUsingTestDatabase();
        
        // Create default OAuth client
        $this->defaultClient = $this->createDefaultOAuthClient();
        
        // Create existing user
        $this->existingUser = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
            'verified' => 1,
        ]);
    }

    /**
     * Test session isolation between different authentication flows
     */
    public function test_session_isolation_between_flows()
    {
        $this->assertUsingTestDatabase();
        $this->createFreshSession();

        // Start with login flow
        $loginParams = $this->createOAuthParams($this->defaultClient->id);
        $this->get('/oauth/authorize?' . http_build_query($loginParams));
        
        // Verify login session data
        $this->assertValidOAuthSession('login');
        $loginRedirectUri = session()->get('redirect_uri');
        
        // Simulate closing tab and opening new one with registration flow
        $this->simulateTabReopen();
        
        // Start registration flow in "new tab"
        $registerParams = $this->createOAuthParams($this->defaultClient->id, [
            'is_register' => '1',
            'email' => '<EMAIL>'
        ]);
        
        $this->get('/oauth/authorize?' . http_build_query($registerParams));
        
        // Should have new session data for registration
        $this->assertValidOAuthSession('registration');
        $registerRedirectUri = session()->get('redirect_uri');
        
        // Session data should be different
        $this->assertNotEquals($loginRedirectUri, $registerRedirectUri);
    }

    /**
     * Test cross-tab behavior simulation
     */
    public function test_cross_tab_behavior_no_interference()
    {
        $this->assertUsingTestDatabase();
        $this->createFreshSession();

        // Tab 1: Start login flow
        $loginParams = $this->createOAuthParams($this->defaultClient->id, [
            'email' => '<EMAIL>'
        ]);
        $this->get('/oauth/authorize?' . http_build_query($loginParams));
        
        // Store Tab 1 session data
        $tab1SessionData = session()->all();
        
        // Simulate opening Tab 2 with different flow
        $this->createFreshSession();
        
        $registerParams = $this->createOAuthParams($this->defaultClient->id, [
            'is_register' => '1',
            'email' => '<EMAIL>'
        ]);
        $this->get('/oauth/authorize?' . http_build_query($registerParams));
        
        // Tab 2 should have its own session data
        $this->assertValidOAuthSession('registration');
        $tab2SessionData = session()->all();
        
        // Sessions should be completely different
        $this->assertNotEquals($tab1SessionData['redirect_uri'], $tab2SessionData['redirect_uri']);
        $this->assertNotEquals($tab1SessionData['oauth_flow_type'], $tab2SessionData['oauth_flow_type']);
    }

    /**
     * Test session cleanup between different flows
     */
    public function test_session_cleanup_between_flows()
    {
        $this->assertUsingTestDatabase();
        $this->createFreshSession();

        // Start with one OAuth flow
        $params1 = $this->createOAuthParams($this->defaultClient->id, [
            'email' => '<EMAIL>'
        ]);
        $this->get('/oauth/authorize?' . http_build_query($params1));
        
        // Verify session has data
        $this->assertValidOAuthSession('login');
        $originalSessionId = session()->getId();
        
        // Start completely different OAuth flow (simulating new request)
        $this->createFreshSession();
        
        $params2 = $this->createOAuthParams($this->defaultClient->id, [
            'is_register' => '1',
            'email' => '<EMAIL>'
        ]);
        $this->get('/oauth/authorize?' . http_build_query($params2));
        
        // Should have new session with different data
        $this->assertValidOAuthSession('registration');
        $newSessionId = session()->getId();
        
        // Session IDs should be different
        $this->assertNotEquals($originalSessionId, $newSessionId);
    }

    /**
     * Test OAuth state parameter persistence
     */
    public function test_oauth_state_parameter_persistence()
    {
        $this->assertUsingTestDatabase();
        $this->createFreshSession();

        $customState = 'custom-state-' . time();
        $oauthParams = $this->createOAuthParams($this->defaultClient->id, [
            'state' => $customState
        ]);

        // Start OAuth flow with custom state
        $this->get('/oauth/authorize?' . http_build_query($oauthParams));
        
        // Verify state is preserved in session
        $redirectUri = session()->get('redirect_uri');
        $this->assertStringContainsString('state=' . $customState, $redirectUri);
        
        // Complete login flow
        $this->post('/login', [
            'email' => $this->existingUser->email,
            'password' => 'password123',
        ]);
        
        // Follow OAuth authorization
        $response = $this->actingAs($this->existingUser)->get('/oauth/authorize?' . http_build_query($oauthParams));
        
        // State should be preserved in final redirect
        $response->assertRedirect();
        $finalRedirectUrl = $response->headers->get('Location');
        $this->assertStringContainsString('state=' . $customState, $finalRedirectUrl);
    }

    /**
     * Test PKCE parameter handling
     */
    public function test_pkce_parameter_handling()
    {
        $this->assertUsingTestDatabase();
        $this->createFreshSession();

        $codeVerifier = base64url_encode(random_bytes(32));
        $codeChallenge = base64url_encode(hash('sha256', $codeVerifier, true));
        
        $oauthParams = $this->createOAuthParams($this->defaultClient->id, [
            'code_challenge' => $codeChallenge,
            'code_challenge_method' => 'S256'
        ]);

        // Start OAuth flow with PKCE
        $this->get('/oauth/authorize?' . http_build_query($oauthParams));
        
        // Verify PKCE parameters are preserved
        $redirectUri = session()->get('redirect_uri');
        $this->assertStringContainsString('code_challenge=' . $codeChallenge, $redirectUri);
        $this->assertStringContainsString('code_challenge_method=S256', $redirectUri);
        
        // Complete authentication
        $this->post('/login', [
            'email' => $this->existingUser->email,
            'password' => 'password123',
        ]);
        
        // PKCE parameters should be maintained through the flow
        $response = $this->actingAs($this->existingUser)->get('/oauth/authorize?' . http_build_query($oauthParams));
        
        $response->assertRedirect();
        $finalRedirectUrl = $response->headers->get('Location');
        $this->assertStringContainsString('code=', $finalRedirectUrl);
    }

    /**
     * Test session data validation for current request
     */
    public function test_session_data_validation_for_current_request()
    {
        $this->assertUsingTestDatabase();
        $this->createFreshSession();

        // Start OAuth flow
        $oauthParams = $this->createOAuthParams($this->defaultClient->id);
        $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Verify session validation works
        $this->assertValidOAuthSession('login');

        // Manually corrupt session data to test validation
        session()->put('oauth_flow_type', 'invalid_flow');

        // Access login page - might redirect if session is invalid
        $response = $this->get('/login');

        // Should either show login page or redirect (both are valid responses)
        $this->assertTrue(
            $response->status() === 200 ||
            $response->isRedirect()
        );

        // If it redirected, follow the redirect
        if ($response->isRedirect()) {
            $response = $this->get($response->headers->get('Location'));
            $response->assertStatus(200);
        }
    }

    /**
     * Test database isolation verification for session tests
     */
    public function test_database_isolation_for_session_tests()
    {
        $this->assertUsingTestDatabase();
        
        // Verify clean database state
        $this->assertEquals(1, User::count()); // Only our test user
        $this->assertEquals(1, Client::count()); // Only our test client
        
        // Create additional test data
        User::factory()->create(['email' => '<EMAIL>']);
        
        $this->assertEquals(2, User::count());
    }
}

/**
 * Helper function for base64url encoding (used in PKCE)
 */
if (!function_exists('base64url_encode')) {
    function base64url_encode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
}

<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

class SessionManagementTest extends TestCase
{
    use RefreshDatabase;

    public function test_login_page_clears_invalid_session_data()
    {
        // Set up invalid session data that should be cleared
        Session::put('redirect_uri', 'invalid-url-without-oauth-params');
        Session::put('is_first_time', true);
        Session::put('oauth_flow_type', 'registration');
        
        // Access login page
        $response = $this->get('/login');
        
        // Should show login page
        $response->assertStatus(200);
        $response->assertViewIs('auth.login');
        
        // Invalid session data should be cleared
        $this->assertFalse(Session::has('redirect_uri'));
        $this->assertFalse(Session::has('oauth_flow_type'));
    }

    public function test_registration_page_clears_conflicting_session_data()
    {
        // Set up session data for login flow
        Session::put('oauth_flow_type', 'login');
        Session::put('redirect_uri', 'some-login-url');
        Session::put('is_first_time', true);
        
        // Access registration page
        $response = $this->get('/register');
        
        // Should show registration page
        $response->assertStatus(200);
        $response->assertViewIs('auth.register');
        
        // Conflicting session data should be cleared
        $this->assertFalse(Session::has('oauth_flow_type'));
    }

    public function test_direct_page_access_without_oauth_parameters()
    {
        // Access login page directly without any session data
        $response = $this->get('/login');
        
        // Should show login page without issues
        $response->assertStatus(200);
        $response->assertViewIs('auth.login');
        
        // Should not have any OAuth session data
        $this->assertFalse(Session::has('redirect_uri'));
        $this->assertFalse(Session::has('oauth_flow_type'));
        $this->assertFalse(Session::has('is_first_time'));
    }

    public function test_registration_page_direct_access()
    {
        // Access registration page directly without any session data
        $response = $this->get('/register');
        
        // Should show registration page without issues
        $response->assertStatus(200);
        $response->assertViewIs('auth.register');
        
        // Should not have any OAuth session data
        $this->assertFalse(Session::has('redirect_uri'));
        $this->assertFalse(Session::has('oauth_flow_type'));
        $this->assertFalse(Session::has('is_first_time'));
    }

    public function test_login_with_registration_parameters_redirects_to_registration()
    {
        // Access login page with registration parameters
        $response = $this->get('/login?is_register=1&email=<EMAIL>');
        
        // Should redirect to registration page with email parameter (URL encoded)
        $response->assertRedirect('/register?email=test%40example.com');
    }

    public function test_login_with_existing_email_shows_login_form()
    {
        // Create a user first
        $user = \App\Models\User::factory()->create([
            'email' => '<EMAIL>',
            'verified' => true,
        ]);
        
        // Access login page with registration parameters for existing email
        $response = $this->get('/login?is_register=1&email=<EMAIL>');
        
        // Should show login form with pre-filled email
        $response->assertStatus(200);
        $response->assertViewIs('auth.login');
        $response->assertViewHas('prefill_email', '<EMAIL>');
    }

    public function test_session_validation_methods_work_correctly()
    {
        // Test the PassportRedirectTrait methods through a controller that uses it
        $this->withoutExceptionHandling();
        
        // Set up valid OAuth session data
        Session::put('redirect_uri', 'http://example.com/oauth/authorize?client_id=123&redirect_uri=http://callback.com&response_type=code');
        Session::put('is_first_time', true);
        Session::put('oauth_flow_type', 'login');
        
        // Access login page - should preserve valid session data
        $response = $this->get('/login');
        
        $response->assertStatus(200);
        
        // Valid session data should be preserved
        $this->assertTrue(Session::has('redirect_uri'));
        $this->assertTrue(Session::has('oauth_flow_type'));
    }
}

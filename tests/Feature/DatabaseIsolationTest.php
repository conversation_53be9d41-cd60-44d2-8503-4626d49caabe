<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class DatabaseIsolationTest extends TestCase
{
    use RefreshDatabase;

    public function test_database_is_isolated_between_tests_first_test()
    {
        // Create a user in the test database
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Isolation Test User'
        ]);

        // Verify the user exists
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>'
        ]);

        // Verify we're using SQLite in memory for testing
        $this->assertEquals('sqlite', config('database.default'));
        $this->assertEquals(':memory:', config('database.connections.sqlite.database'));
        
        // Count total users
        $userCount = User::count();
        $this->assertEquals(1, $userCount);
    }

    public function test_database_is_isolated_between_tests_second_test()
    {
        // This test should start with a fresh database
        // The user from the previous test should not exist
        $this->assertDatabaseMissing('users', [
            'email' => '<EMAIL>'
        ]);

        // Count should be 0 (fresh database)
        $userCount = User::count();
        $this->assertEquals(0, $userCount);

        // Create a different user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Second Test User'
        ]);

        // Verify only this user exists
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>'
        ]);
        
        $userCount = User::count();
        $this->assertEquals(1, $userCount);
    }

    public function test_development_database_configuration_is_preserved()
    {
        // When not in testing mode, we should be using MySQL
        // But since we're in testing, we should be using SQLite
        $this->assertEquals('testing', app()->environment());
        $this->assertEquals('sqlite', config('database.default'));
        
        // Verify that the original .env file still has MySQL configuration
        // (This is just to demonstrate that we haven't changed the development setup)
        $envContent = file_get_contents(base_path('.env'));
        $this->assertStringContainsString('DB_CONNECTION=mysql', $envContent);
        $this->assertStringContainsString('DB_DATABASE=gravitywrite_db', $envContent);
    }
}

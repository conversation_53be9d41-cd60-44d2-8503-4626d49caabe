<?php

namespace Tests;

use Illuminate\Foundation\Testing\TestCase as BaseTestCase;

abstract class TestCase extends BaseTestCase
{
    /**
     * Setup the test environment.
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Ensure we're using the testing environment
        $this->app['env'] = 'testing';
    }

    /**
     * Assert that we're using the SQLite testing database.
     */
    protected function assertUsingTestDatabase(): void
    {
        $this->assertEquals('testing', app()->environment());
        $this->assertEquals('sqlite', config('database.default'));
        $this->assertEquals(':memory:', config('database.connections.sqlite.database'));
    }

    /**
     * Assert that development database configuration is preserved.
     */
    protected function assertDevelopmentDatabasePreserved(): void
    {
        // Verify that the original .env file still has MySQL configuration
        $envContent = file_get_contents(base_path('.env'));
        $this->assertStringContainsString('DB_CONNECTION=mysql', $envContent);
        $this->assertStringContainsString('DB_DATABASE=gravitywrite_db', $envContent);
    }

    /**
     * Get the current database connection info for debugging.
     */
    protected function getDatabaseInfo(): array
    {
        return [
            'environment' => app()->environment(),
            'default_connection' => config('database.default'),
            'sqlite_database' => config('database.connections.sqlite.database'),
            'mysql_database' => config('database.connections.mysql.database'),
        ];
    }

    /**
     * Create a fresh session for testing cross-tab behavior.
     */
    protected function createFreshSession(): void
    {
        // Clear all session data
        session()->flush();
        session()->regenerate();

        // Ensure we start with a clean session (no OAuth-related data)
        $this->assertCleanSession();
    }

    /**
     * Create a default OAuth client for testing.
     */
    protected function createDefaultOAuthClient(): \App\Models\Passport\Client
    {
        return \App\Models\Passport\Client::create([
            'id' => 'test-default-client',
            'name' => 'GravityWriteDefaultRedirect',
            'secret' => 'test-secret',
            'redirect' => 'http://test.localhost/oauth/callback,http://app.test.localhost/oauth/callback',
            'personal_access_client' => false,
            'password_client' => false,
            'revoked' => false,
        ]);
    }

    /**
     * Create OAuth authorization parameters.
     */
    protected function createOAuthParams(string $clientId, array $additionalParams = []): array
    {
        return array_merge([
            'client_id' => $clientId,
            'redirect_uri' => 'http://test.localhost/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
            'state' => \Illuminate\Support\Str::random(40),
        ], $additionalParams);
    }

    /**
     * Assert that session contains valid OAuth data.
     */
    protected function assertValidOAuthSession(string $expectedFlowType = null): void
    {
        $this->assertTrue(session()->has('redirect_uri'));
        $this->assertTrue(session()->has('is_first_time'));

        if ($expectedFlowType) {
            $this->assertEquals($expectedFlowType, session()->get('oauth_flow_type'));
        }
    }

    /**
     * Assert that session is clean (no OAuth data).
     */
    protected function assertCleanSession(): void
    {
        $this->assertFalse(session()->has('redirect_uri'));
        $this->assertFalse(session()->has('is_first_time'));
        $this->assertFalse(session()->has('oauth_flow_type'));
        $this->assertFalse(session()->has('authRequest'));
    }

    /**
     * Simulate closing and reopening browser tab.
     */
    protected function simulateTabReopen(): void
    {
        // Store current session data
        $sessionData = session()->all();

        // Simulate tab close/reopen by creating new session but keeping data
        session()->flush();
        session()->regenerate();

        // Restore session data (simulating browser session persistence)
        foreach ($sessionData as $key => $value) {
            session()->put($key, $value);
        }
    }
}

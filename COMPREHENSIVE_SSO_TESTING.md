# Comprehensive Laravel Passport SSO Integration Testing

## Overview

This document describes the comprehensive integration test suite for the Laravel Passport SSO authentication system. The tests verify all authentication flows work correctly with proper session handling and state management.

## Test Coverage

### 🔍 **Direct Access Tests**
- **Direct Login Access** - Verifies login page loads without redirect loops when accessed directly
- **Direct Register Access** - Verifies registration page loads without redirect loops when accessed directly

### 🔐 **OAuth Parameter Handling Tests**
- **Login with Email Parameter** - When email parameter is provided, verifies it shows login form (should NOT trigger registration flow)
- **Standard OAuth Flow** - Verifies unauthenticated users are redirected to login page during OAuth authorization

### 📝 **Registration Flow Tests**
- **Registration Flow (No Email)** - Verifies redirect to registration page when no email provided
- **Registration Flow (New Email)** - Verifies redirect to registration page with email pre-filled when new email provided
- **Registration Flow (Existing Email)** - Verifies redirect to login form with email pre-filled when existing email provided

### ✅ **Post-Authentication Redirect Tests**
- **After Successful Authentication** - Verifies redirect to GravityWriteDefaultRedirect client's first redirect URI with valid authorization code

### 🔄 **Client Redirect with is_register Parameter Tests**
- **is_register=1 (No Email)** - Verifies redirect to registration page
- **is_register=1 (Existing Email)** - Verifies redirect to login form with email pre-filled
- **is_register=1 (New Email)** - Verifies redirect to registration page with email pre-filled

### 🗂️ **Critical Session Management Tests**
- **Session Isolation** - Each test starts with a clean session state
- **Cross-Tab Behavior** - Simulates closing browser tab mid-flow and reopening with different flow
- **Session Cleanup** - Verifies proper session cleanup between different authentication flows
- **State Persistence** - Verifies OAuth state parameters are maintained correctly throughout flows

### 🛡️ **Security and Edge Case Tests**
- **Unverified User Handling** - Tests OAuth flow with unverified users
- **Invalid Client ID** - Tests handling of invalid OAuth clients
- **Invalid Redirect URI** - Tests protection against malicious redirect URIs
- **Session Tampering** - Tests protection against session data tampering
- **Concurrent Requests** - Tests handling of concurrent OAuth requests
- **Malformed Parameters** - Tests graceful handling of malformed OAuth parameters

## Test Files

### Core Test Files
1. **`tests/Feature/Auth/PassportSsoComprehensiveTest.php`** - Main comprehensive authentication flow tests
2. **`tests/Feature/Auth/PassportSsoSessionManagementTest.php`** - Session isolation and management tests
3. **`tests/Feature/Auth/PassportSsoClientRedirectTest.php`** - Client redirect with is_register parameter tests
4. **`tests/Feature/Auth/PassportSsoEdgeCasesTest.php`** - Edge cases and security scenario tests

### Supporting Files
- **`tests/TestCase.php`** - Enhanced base test class with SSO testing helpers
- **`run_comprehensive_sso_tests.sh`** - Automated test runner script

## Database Isolation

### Configuration
- **Development Environment**: Uses MySQL database (`gravitywrite_db`)
- **Testing Environment**: Uses SQLite in-memory database (`:memory:`)
- **Complete Isolation**: Tests never affect development data

### Verification
Each test file includes explicit database isolation verification:
```php
protected function setUp(): void
{
    parent::setUp();
    
    // Verify database isolation
    $this->assertUsingTestDatabase();
    
    // Test setup...
}
```

## Running Tests

### Run All Comprehensive Tests
```bash
./run_comprehensive_sso_tests.sh
```

### Run Individual Test Categories
```bash
# Database isolation verification
php artisan test --env=testing tests/Feature/DatabaseIsolationTest.php

# Comprehensive authentication flows
php artisan test --env=testing tests/Feature/Auth/PassportSsoComprehensiveTest.php

# Session management tests
php artisan test --env=testing tests/Feature/Auth/PassportSsoSessionManagementTest.php

# Client redirect tests
php artisan test --env=testing tests/Feature/Auth/PassportSsoClientRedirectTest.php

# Edge cases and security tests
php artisan test --env=testing tests/Feature/Auth/PassportSsoEdgeCasesTest.php
```

### Run Specific Test Method
```bash
php artisan test --env=testing --filter test_direct_login_access_loads_without_redirect_loops
```

## Test Implementation Requirements

### ✅ **Database Isolation**
- Uses SQLite in-memory database with `RefreshDatabase` trait
- Explicit verification that tests use SQLite and don't affect MySQL development database
- Clean database state for each test

### ✅ **Session Management**
- Fresh session state for each test using `createFreshSession()` helper
- Cross-tab behavior simulation with `simulateTabReopen()` helper
- Session validation and cleanup verification

### ✅ **OAuth Flow Testing**
- Complete OAuth authorization code flow testing
- PKCE parameter handling and validation
- State parameter persistence verification
- Client redirect URI validation

### ✅ **Security Testing**
- Protection against session tampering
- Invalid client and redirect URI handling
- Malformed parameter graceful handling
- Concurrent request management

## Key Test Scenarios Covered

### 🔄 **Flow Switching Scenarios**
1. **Login → Registration**: Start login flow, switch to registration
2. **Registration → Login**: Start registration flow, switch to login
3. **Cross-Tab Interference**: Multiple tabs with different flows
4. **Session Persistence**: Verify data persists correctly across page loads

### 📧 **Email Parameter Scenarios**
1. **No Email**: Standard OAuth flow without email parameter
2. **New Email**: Email not in database (triggers registration)
3. **Existing Email**: Email exists in database (triggers login)
4. **Special Characters**: Email with special characters handling

### 🔐 **Authentication State Scenarios**
1. **Unauthenticated**: Standard OAuth flow for guest users
2. **Authenticated**: OAuth flow for already logged-in users
3. **Unverified**: OAuth flow for users with unverified emails
4. **Post-Authentication**: Redirect handling after successful authentication

## Expected Test Results

When all tests pass, you can be confident that:

✅ **Authentication flows work correctly** without redirect loops or session interference  
✅ **Session management is robust** with proper isolation between different flows  
✅ **OAuth parameters are handled correctly** including state, PKCE, and custom parameters  
✅ **Security measures are in place** to prevent tampering and malicious requests  
✅ **Database isolation is working** ensuring tests don't affect development data  
✅ **Edge cases are handled gracefully** without breaking the authentication system  

## Troubleshooting

### If Tests Fail
1. **Check Database Isolation**: Ensure SQLite is installed and configured
2. **Verify Environment**: Make sure you're using `--env=testing` flag
3. **Review Session Configuration**: Check `.env.testing` session settings
4. **Check OAuth Client Setup**: Verify test clients are created correctly

### Common Issues
- **Session Driver**: Ensure testing uses `array` session driver
- **Database Connection**: Verify SQLite in-memory database is working
- **OAuth Configuration**: Check Passport routes and middleware setup
- **Email Verification**: Ensure test users have proper verification status

## Integration with CI/CD

Add to your CI/CD pipeline:
```yaml
- name: Run Comprehensive SSO Tests
  run: |
    php artisan test --env=testing tests/Feature/DatabaseIsolationTest.php
    php artisan test --env=testing tests/Feature/Auth/PassportSsoComprehensiveTest.php
    php artisan test --env=testing tests/Feature/Auth/PassportSsoSessionManagementTest.php
    php artisan test --env=testing tests/Feature/Auth/PassportSsoClientRedirectTest.php
    php artisan test --env=testing tests/Feature/Auth/PassportSsoEdgeCasesTest.php
```

This comprehensive test suite ensures your Laravel Passport SSO authentication system is robust, secure, and handles all edge cases correctly!

<?php

namespace Database\Factories\Passport;

use App\Models\Passport\OAuthSession;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Passport\OAuthSession>
 */
class OAuthSessionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = OAuthSession::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'oauth_access_token_id' => null,
            'ip_address' => $this->faker->ipv4(),
            'user_agent' => $this->faker->userAgent(),
            'code' => Str::random(40),
            'token' => null,
        ];
    }

    /**
     * Indicate that the session has a token.
     */
    public function withToken(): static
    {
        return $this->state(fn(array $attributes) => [
            'token' => base64_encode(Str::random(80)),
        ]);
    }

    /**
     * Set a specific user for the session.
     */
    public function forUser(User $user): static
    {
        return $this->state(fn(array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    /**
     * Set a specific IP address for the session.
     */
    public function withIpAddress(string $ipAddress): static
    {
        return $this->state(fn(array $attributes) => [
            'ip_address' => $ipAddress,
        ]);
    }

    /**
     * Set a specific user agent for the session.
     */
    public function withUserAgent(string $userAgent): static
    {
        return $this->state(fn(array $attributes) => [
            'user_agent' => $userAgent,
        ]);
    }
}

<?php

namespace Database\Factories\Passport;

use App\Models\Passport\RefreshToken;
use App\Models\Passport\Token;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Passport\RefreshToken>
 */
class RefreshTokenFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = RefreshToken::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => Str::random(80),
            'access_token_id' => Token::factory(),
            'revoked' => false,
            'expires_at' => now()->addDays(30),
        ];
    }

    /**
     * Indicate that the refresh token is revoked.
     */
    public function revoked(): static
    {
        return $this->state(fn(array $attributes) => [
            'revoked' => true,
        ]);
    }

    /**
     * Indicate that the refresh token is expired.
     */
    public function expired(): static
    {
        return $this->state(fn(array $attributes) => [
            'expires_at' => now()->subDay(),
        ]);
    }

    /**
     * Set a specific access token for the refresh token.
     */
    public function forAccessToken(Token $accessToken): static
    {
        return $this->state(fn(array $attributes) => [
            'access_token_id' => $accessToken->id,
        ]);
    }
}

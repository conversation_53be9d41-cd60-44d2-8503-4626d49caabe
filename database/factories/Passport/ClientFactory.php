<?php

namespace Database\Factories\Passport;

use App\Models\Passport\Client;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Passport\Client>
 */
class ClientFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Client::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => Str::uuid()->toString(),
            'user_id' => null,
            'name' => $this->faker->company() . ' OAuth Client',
            'secret' => Str::random(40),
            'provider' => null,
            'domain' => $this->faker->domainName(),
            'redirect' => 'http://localhost:8000/oauth/callback',
            'personal_access_client' => false,
            'password_client' => false,
            'revoked' => false,
        ];
    }

    /**
     * Indicate that the client is a personal access client.
     */
    public function personalAccess(): static
    {
        return $this->state(fn(array $attributes) => [
            'personal_access_client' => true,
        ]);
    }

    /**
     * Indicate that the client is a password client.
     */
    public function password(): static
    {
        return $this->state(fn(array $attributes) => [
            'password_client' => true,
        ]);
    }

    /**
     * Indicate that the client is revoked.
     */
    public function revoked(): static
    {
        return $this->state(fn(array $attributes) => [
            'revoked' => true,
        ]);
    }

    /**
     * Set a specific redirect URI for the client.
     */
    public function withRedirect(string $redirect): static
    {
        return $this->state(fn(array $attributes) => [
            'redirect' => $redirect,
        ]);
    }

    /**
     * Set wildcard redirect URI for testing.
     */
    public function withWildcardRedirect(): static
    {
        return $this->state(fn(array $attributes) => [
            'redirect' => 'https://*.ngrok-free.app/oauth/callback',
        ]);
    }

    /**
     * Create a client for SSO testing.
     */
    public function sso(): static
    {
        return $this->state(fn(array $attributes) => [
            'name' => 'SSO Test Client',
            'redirect' => 'http://sso.test.com/oauth/callback',
        ]);
    }
}

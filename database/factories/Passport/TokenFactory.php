<?php

namespace Database\Factories\Passport;

use App\Models\Passport\Client;
use App\Models\Passport\Token;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Passport\Token>
 */
class TokenFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Token::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => Str::random(80),
            'user_id' => User::factory(),
            'client_id' => Client::factory(),
            'name' => null,
            'scopes' => json_encode([]),
            'revoked' => false,
            'expires_at' => now()->addDays(15),
        ];
    }

    /**
     * Indicate that the token is revoked.
     */
    public function revoked(): static
    {
        return $this->state(fn(array $attributes) => [
            'revoked' => true,
        ]);
    }

    /**
     * Indicate that the token is expired.
     */
    public function expired(): static
    {
        return $this->state(fn(array $attributes) => [
            'expires_at' => now()->subDay(),
        ]);
    }

    /**
     * Set specific scopes for the token.
     */
    public function withScopes(array $scopes): static
    {
        return $this->state(fn(array $attributes) => [
            'scopes' => json_encode($scopes),
        ]);
    }

    /**
     * Set a specific user for the token.
     */
    public function forUser(User $user): static
    {
        return $this->state(fn(array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    /**
     * Set a specific client for the token.
     */
    public function forClient(Client $client): static
    {
        return $this->state(fn(array $attributes) => [
            'client_id' => $client->id,
        ]);
    }

    /**
     * Set a specific name for the token.
     */
    public function withName(string $name): static
    {
        return $this->state(fn(array $attributes) => [
            'name' => $name,
        ]);
    }

    /**
     * Create a personal access token.
     */
    public function personalAccess(): static
    {
        return $this->state(fn(array $attributes) => [
            'name' => 'Personal Access Token',
        ]);
    }
}

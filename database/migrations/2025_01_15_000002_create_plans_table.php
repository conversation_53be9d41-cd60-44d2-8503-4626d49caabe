<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('plans')) {
            return;
        }

        Schema::create('plans', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('price', 8, 2)->default(0);
            $table->integer('word_limit')->default(0);
            $table->integer('image_limit')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Insert default plans
        DB::table('plans')->insert([
            [
                'id' => 1,
                'name' => 'Free Plan',
                'description' => 'Basic free plan',
                'price' => 0,
                'word_limit' => 1000,
                'image_limit' => 10,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 2,
                'name' => 'Pro Plan',
                'description' => 'Professional plan',
                'price' => 29.99,
                'word_limit' => 50000,
                'image_limit' => 500,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plans');
    }
};

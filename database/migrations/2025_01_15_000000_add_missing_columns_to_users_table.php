<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add missing columns that the application expects
            if (!Schema::hasColumn('users', 'verified')) {
                $table->boolean('verified')->default(false)->after('email_verified_at');
            }
            
            if (!Schema::hasColumn('users', 'verified_at')) {
                $table->timestamp('verified_at')->nullable()->after('verified');
            }
            
            if (!Schema::hasColumn('users', 'mailerlite_subscriber_id')) {
                $table->string('mailerlite_subscriber_id')->nullable()->after('verified_at');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['verified', 'verified_at', 'mailerlite_subscriber_id']);
        });
    }
};

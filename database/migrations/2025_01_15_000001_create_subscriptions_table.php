<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('subscriptions')) {
            return;
        }

        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('plan_id')->default(1);
            $table->timestamp('start_at')->nullable();
            $table->timestamp('end_at')->nullable();
            $table->timestamp('canceled_at')->nullable();
            $table->string('status')->default('active');
            $table->text('cancelled_reason')->nullable();
            $table->string('payment_frequency')->nullable();
            $table->integer('multiprompt_company_usage')->default(0);
            $table->integer('image_bg_remover_usage')->default(0);
            $table->integer('template_run_usage')->default(0);
            $table->string('pp_subscription')->nullable();
            $table->string('stripe_subscription')->nullable();
            $table->string('razorpay_subscription')->nullable();
            $table->integer('chat_usage')->default(0);
            $table->integer('image_prompt_usage')->default(0);
            $table->integer('stock_img_usage')->default(0);
            $table->integer('credits')->default(0);
            $table->string('add_on_plan')->nullable();
            $table->integer('add_on_total_credit')->default(0);
            $table->integer('add_on_word_usage')->default(0);
            $table->integer('add_on_image_usage')->default(0);
            $table->integer('add_on_voice_usage')->default(0);
            $table->unsignedBigInteger('plan_credits_id')->nullable();
            $table->integer('plan_total_credit')->default(0);
            $table->integer('plan_word_usage')->default(0);
            $table->integer('plan_image_usage')->default(0);
            $table->integer('plan_voice_usage')->default(0);
            $table->integer('usage')->default(0);
            $table->integer('long_blog_usage')->default(0);
            $table->integer('long_blog_image_usage')->default(0);
            $table->integer('image_usage')->default(0);
            $table->boolean('is_react')->default(true);
            $table->timestamp('last_reset_date')->nullable();
            $table->timestamp('reset_expiry_date')->nullable();
            $table->boolean('is_admin')->default(false);
            $table->boolean('is_manual')->default(false);
            $table->integer('site_import_usage')->default(0);
            $table->integer('storyboard_usage')->default(0);
            $table->integer('storyboard_image_usage')->default(0);
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};

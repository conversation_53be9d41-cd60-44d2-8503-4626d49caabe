#!/bin/bash

# Core Laravel Passport SSO Integration Tests Runner
# This script runs the essential SSO authentication flow tests

echo "🚀 Running Core Laravel Passport SSO Integration Tests"
echo "======================================================"

# Change to the project directory
cd /Applications/XAMPP/xamppfiles/htdocs/gravitywrite/gravitywrite-auth

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}✅ $message: PASSED${NC}"
    elif [ "$status" = "FAIL" ]; then
        echo -e "${RED}❌ $message: FAILED${NC}"
    elif [ "$status" = "INFO" ]; then
        echo -e "${BLUE}ℹ️  $message${NC}"
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}⚠️  $message${NC}"
    fi
}

# Function to run individual test method
run_test_method() {
    local test_file=$1
    local test_method=$2
    local test_name=$3
    
    print_status "INFO" "Running $test_name..."
    
    if php artisan test --env=testing "$test_file" --filter="$test_method" --stop-on-failure; then
        print_status "PASS" "$test_name"
        return 0
    else
        print_status "FAIL" "$test_name"
        return 1
    fi
}

echo ""
print_status "INFO" "Core Test Categories:"
echo "1. Database Isolation Verification"
echo "2. Direct Access Tests"
echo "3. OAuth Parameter Handling"
echo "4. Session Management"
echo "5. Authentication Flows"
echo ""

# Initialize test results
total_tests=0
passed_tests=0
failed_tests=0

# Test 1: Database Isolation Verification
echo "🔍 Testing Database Isolation..."
if run_test_method "tests/Feature/DatabaseIsolationTest.php" "test_database_is_isolated_between_tests_first_test" "Database Isolation"; then
    ((passed_tests++))
else
    ((failed_tests++))
    echo "❌ Database isolation failed - stopping tests for safety"
    exit 1
fi
((total_tests++))

echo ""

# Test 2: Direct Access Tests
echo "🔐 Testing Direct Access..."
if run_test_method "tests/Feature/Auth/PassportSsoComprehensiveTest.php" "test_direct_login_access_loads_without_redirect_loops" "Direct Login Access"; then
    ((passed_tests++))
else
    ((failed_tests++))
fi
((total_tests++))

if run_test_method "tests/Feature/Auth/PassportSsoComprehensiveTest.php" "test_direct_register_access_loads_without_redirect_loops" "Direct Register Access"; then
    ((passed_tests++))
else
    ((failed_tests++))
fi
((total_tests++))

echo ""

# Test 3: OAuth Parameter Handling
echo "🔄 Testing OAuth Parameter Handling..."
if run_test_method "tests/Feature/Auth/PassportSsoComprehensiveTest.php" "test_standard_oauth_flow_redirects_unauthenticated_to_login" "Standard OAuth Flow"; then
    ((passed_tests++))
else
    ((failed_tests++))
fi
((total_tests++))

if run_test_method "tests/Feature/Auth/PassportSsoComprehensiveTest.php" "test_login_with_email_parameter_shows_login_form" "Login with Email Parameter"; then
    ((passed_tests++))
else
    ((failed_tests++))
fi
((total_tests++))

echo ""

# Test 4: Session Management
echo "🗂️  Testing Session Management..."
if run_test_method "tests/Feature/Auth/PassportSsoSessionManagementTest.php" "test_session_isolation_between_flows" "Session Isolation"; then
    ((passed_tests++))
else
    ((failed_tests++))
fi
((total_tests++))

if run_test_method "tests/Feature/Auth/PassportSsoSessionManagementTest.php" "test_cross_tab_behavior_no_interference" "Cross-Tab Behavior"; then
    ((passed_tests++))
else
    ((failed_tests++))
fi
((total_tests++))

echo ""

# Test 5: Authentication Flows
echo "✅ Testing Authentication Flows..."
if run_test_method "tests/Feature/Auth/PassportSsoComprehensiveTest.php" "test_registration_flow_no_email_redirects_to_register" "Registration Flow (No Email)"; then
    ((passed_tests++))
else
    ((failed_tests++))
fi
((total_tests++))

if run_test_method "tests/Feature/Auth/PassportSsoComprehensiveTest.php" "test_registration_flow_existing_email_redirects_to_login" "Registration Flow (Existing Email)"; then
    ((passed_tests++))
else
    ((failed_tests++))
fi
((total_tests++))

echo ""

# Test 6: Edge Cases
echo "🛡️  Testing Edge Cases..."
if run_test_method "tests/Feature/Auth/PassportSsoEdgeCasesTest.php" "test_oauth_flow_with_unverified_user" "Unverified User Handling"; then
    ((passed_tests++))
else
    ((failed_tests++))
fi
((total_tests++))

echo ""
echo "======================================================"
echo "📊 CORE TEST SUMMARY"
echo "======================================================"

if [ $failed_tests -eq 0 ]; then
    print_status "PASS" "All core tests completed successfully!"
    echo -e "${GREEN}🎉 Total: $total_tests | Passed: $passed_tests | Failed: $failed_tests${NC}"
    echo ""
    echo "✅ Your Laravel Passport SSO authentication system core functionality is working!"
    echo "✅ Database isolation is properly configured"
    echo "✅ Direct access to auth pages works correctly"
    echo "✅ OAuth parameter handling is functional"
    echo "✅ Session management is working"
    echo "✅ Basic authentication flows are operational"
else
    print_status "FAIL" "Some core tests failed!"
    echo -e "${RED}💥 Total: $total_tests | Passed: $passed_tests | Failed: $failed_tests${NC}"
    echo ""
    echo "❌ Please review the failed tests and fix any issues"
    echo "❌ Check the test output above for specific error details"
fi

echo ""
echo "======================================================"
echo "📝 NEXT STEPS"
echo "======================================================"

if [ $failed_tests -eq 0 ]; then
    echo "✅ Core SSO functionality is working correctly!"
    echo "✅ You can now run the comprehensive test suite: ./run_comprehensive_sso_tests.sh"
    echo "✅ Consider running these tests in your CI/CD pipeline"
else
    echo "🔧 Fix the failing core tests before proceeding"
    echo "🔧 Review authentication logic and session management"
    echo "🔧 Check OAuth client configuration"
fi

echo ""
echo "To run individual tests:"
echo "• php artisan test --env=testing tests/Feature/Auth/PassportSsoComprehensiveTest.php"
echo "• php artisan test --env=testing tests/Feature/Auth/PassportSsoSessionManagementTest.php"
echo "• php artisan test --env=testing tests/Feature/Auth/PassportSsoEdgeCasesTest.php"

echo ""
echo "🏁 Core test run completed at $(date)"

# Exit with appropriate code
if [ $failed_tests -eq 0 ]; then
    exit 0
else
    exit 1
fi

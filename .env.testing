APP_NAME="GravityWrite Auth"
APP_ENV=testing
APP_KEY=base64:CNtsbOXfe8gXOTGd6x3yTGIaWHcTXxaY3kBPJHMaGaE=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://127.0.0.1:8002

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=4

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Test Database Configuration - SQLite in memory
DB_CONNECTION=sqlite
DB_DATABASE=:memory:

# Test Session Configuration
SESSION_DRIVER=array
SESSION_LIFETIME=120
SESSION_ENCRYPT=false

# Test Broadcasting and Queue Configuration
BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync

# Test Cache Configuration
CACHE_STORE=array
CACHE_PREFIX=

# Test Mail Configuration
MAIL_MAILER=array

# Test Redis Configuration (if needed)
REDIS_CLIENT=phpredis

VITE_APP_NAME="${APP_NAME}"

# OAuth Configuration for Testing (use test values)
GOOGLE_CLIENT_ID=test_google_client_id
GOOGLE_CLIENT_SECRET=test_google_client_secret
GOOGLE_REDIRECT_URI=http://127.0.0.1:8002/oauth/google/callback

GITHUB_CLIENT_ID=test_github_client_id
GITHUB_CLIENT_SECRET=test_github_client_secret
GITHUB_REDIRECT_URI=http://127.0.0.1:8002/oauth/github/callback

EMAIL_VERIFIER_KEY=test_email_verifier_key

GW_URL=http://test.localhost
FE_URL=http://127.0.0.1:8000
MAILER_LITE_API_KEY=test_mailer_lite_api_key

# Disable external services during testing
PULSE_ENABLED=false
TELESCOPE_ENABLED=false

# Testing Setup Documentation

## Overview

This Laravel application now has a robust testing environment with proper database isolation. Your tests run against a separate SQLite in-memory database, ensuring that your development database (`gravitywrite_db`) remains completely untouched during testing.

## Configuration

### Database Isolation

- **Development Environment**: Uses MySQL database (`gravitywrite_db`)
- **Testing Environment**: Uses SQLite in-memory database (`:memory:`)
- **Complete Isolation**: Tests never affect your development data

### Key Files

1. **`phpunit.xml`**: Configures PHPUnit to use SQLite for testing
2. **`.env.testing`**: Testing environment configuration
3. **`tests/TestCase.php`**: Base test class with proper setup
4. **`tests/Feature/DatabaseIsolationTest.php`**: Demonstrates database isolation

## How It Works

### Environment Switching

When you run tests with `php artisan test --env=testing`, <PERSON><PERSON> automatically:

1. Switches to the testing environment
2. Uses SQLite in-memory database instead of MySQL
3. Applies all testing-specific configurations from `.env.testing`
4. Ensures complete isolation from your development environment

### Database Refresh

The `RefreshDatabase` trait in your tests:

- Creates a fresh SQLite database for each test
- Runs migrations automatically
- Cleans up after each test
- Never touches your MySQL development database

## Running Tests

### Run All Tests
```bash
php artisan test --env=testing
```

### Run Specific Test File
```bash
php artisan test --env=testing tests/Feature/SessionManagementTest.php
```

### Run Specific Test Method
```bash
php artisan test --env=testing --filter test_login_page_clears_invalid_session_data
```

### Run Tests with Coverage (if configured)
```bash
php artisan test --env=testing --coverage
```

## Verification

### Check Database Isolation
Run the database isolation test to verify everything is working:
```bash
php artisan test --env=testing tests/Feature/DatabaseIsolationTest.php
```

This test verifies:
- Tests use SQLite in-memory database
- Data doesn't persist between tests
- Development configuration is preserved

### Check Development Database
Verify your development database is still intact:
```bash
php artisan tinker --execute="echo 'DB: ' . config('database.default') . ' - ' . config('database.connections.mysql.database');"
```

## Benefits

1. **Safe Testing**: Your development data is never at risk
2. **Fast Tests**: SQLite in-memory database is extremely fast
3. **Isolated Tests**: Each test starts with a clean database
4. **Consistent Environment**: Tests run the same way every time
5. **No Setup Required**: Database is created automatically for each test

## Best Practices

1. **Always use `RefreshDatabase`** in tests that interact with the database
2. **Use factories** for creating test data instead of hardcoded values
3. **Test in isolation** - don't rely on data from other tests
4. **Run tests frequently** to catch issues early
5. **Use `--env=testing`** flag to ensure proper environment

## Troubleshooting

### If tests fail to run:
1. Ensure SQLite is installed: `php -m | grep sqlite`
2. Check that `.env.testing` exists and is properly configured
3. Verify `phpunit.xml` has the correct database settings

### If tests affect development data:
1. Make sure you're using the `--env=testing` flag
2. Check that `RefreshDatabase` trait is being used
3. Verify the test environment is properly configured

## Example Test Structure

```php
<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ExampleTest extends TestCase
{
    use RefreshDatabase; // This ensures database isolation

    public function test_example()
    {
        // Create test data
        $user = User::factory()->create();
        
        // Test your functionality
        $response = $this->actingAs($user)->get('/dashboard');
        
        // Assert results
        $response->assertStatus(200);
    }
}
```

This setup ensures you can run tests repeatedly without any risk to your development database!

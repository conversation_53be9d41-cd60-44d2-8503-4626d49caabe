<?php

namespace App\Socialite;

use <PERSON><PERSON>\Socialite\Two\AbstractProvider;
use <PERSON><PERSON>\Socialite\Two\ProviderInterface;
use <PERSON><PERSON>\Socialite\Two\User;

/**
 * GravityProvider - Custom Socialite provider for self-referential OAuth.
 *
 * This provider allows the application to act as both OAuth client and server,
 * enabling SSO flows where the application redirects to its own OAuth authorization
 * endpoint with custom parameters like is_register and email.
 */
class GravityProvider extends AbstractProvider implements ProviderInterface
{
    /**
     * The base URL for the OAuth server.
     *
     * @var string
     */
    protected $baseUrl;

    /**
     * Create a new provider instance.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $clientId
     * @param  string  $clientSecret
     * @param  string  $redirectUrl
     * @param  array  $config
     * @return void
     */
    public function __construct($request, $clientId, $clientSecret, $redirectUrl, array $config = [])
    {
        parent::__construct($request, $clientId, $clientSecret, $redirectUrl);
        
        $this->baseUrl = $config['base_url'] ?? config('app.url');
    }

    /**
     * Get the authentication URL for the provider.
     *
     * @param  string  $state
     * @return string
     */
    protected function getAuthUrl($state)
    {
        return $this->buildAuthUrlFromBase($this->baseUrl . '/oauth/authorize', $state);
    }

    /**
     * Get the token URL for the provider.
     *
     * @return string
     */
    protected function getTokenUrl()
    {
        return $this->baseUrl . '/oauth/token';
    }

    /**
     * Get the raw user for the given access token.
     *
     * @param  string  $token
     * @return array
     */
    protected function getUserByToken($token)
    {
        $response = $this->getHttpClient()->get($this->baseUrl . '/api/user', [
            'headers' => [
                'Authorization' => 'Bearer ' . $token,
                'Accept' => 'application/json',
            ],
        ]);

        return json_decode($response->getBody(), true);
    }

    /**
     * Map the raw user array to a Socialite User instance.
     *
     * @param  array  $user
     * @return \Laravel\Socialite\Two\User
     */
    protected function mapUserToObject(array $user)
    {
        return (new User)->setRaw($user)->map([
            'id'       => $user['id'],
            'nickname' => $user['name'],
            'name'     => $user['name'],
            'email'    => $user['email'],
            'avatar'   => null,
        ]);
    }

    /**
     * Get the authorization URL with custom parameters.
     *
     * @param  array  $options
     * @return string
     */
    public function redirect($options = [])
    {
        $state = null;
        
        if ($this->usesState()) {
            $this->request->session()->put('state', $state = $this->getState());
        }

        return $this->getAuthUrl($state);
    }

    /**
     * Build the authentication URL for the OAuth provider with custom parameters.
     *
     * @param  string  $url
     * @param  string  $state
     * @return string
     */
    protected function buildAuthUrlFromBase($url, $state)
    {
        $query = http_build_query($this->getCodeFields($state), '', '&', $this->encodingType);

        return $url . '?' . $query;
    }

    /**
     * Get the GET parameters for the code request.
     *
     * @param  string|null  $state
     * @return array
     */
    protected function getCodeFields($state = null)
    {
        $fields = [
            'client_id' => $this->clientId,
            'redirect_uri' => $this->redirectUrl,
            'scope' => $this->formatScopes($this->getScopes(), $this->scopeSeparator),
            'response_type' => 'code',
        ];

        if ($this->usesState()) {
            $fields['state'] = $state;
        }

        // Add any custom parameters that were passed via with()
        if (!empty($this->parameters)) {
            $fields = array_merge($fields, $this->parameters);
        }

        return $fields;
    }
}

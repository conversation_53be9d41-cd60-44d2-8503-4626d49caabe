<?php

namespace App\Traits\Passport;

use App\Models\Passport\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

trait PassportRedirectTrait
{
    public const DEFAULT_CLIENT_NAME = 'GravityWriteDefaultRedirect';

    /**
     * Set the default redirection when the user is not authenticated
     *
     * It looks for a client named "GravityWriteDefaultRedirect" and uses its first redirect URI
     * to build the default redirection URL.
     *
     * @return void
     */
    public function setDefaultRedirection(): void
    {
        if (request()->session()->has('redirect_uri')) {
            return;
        }

        $client = Client::where('name', self::DEFAULT_CLIENT_NAME)->first();

        // Only set default redirection if the client exists
        if (!$client) {
            Log::warning('Default OAuth client "' . self::DEFAULT_CLIENT_NAME . '" not found in database');
            return;
        }

        $redirectTo = explode(',', (string) $client->redirect)[0] ?? '';

        // Only proceed if we have a valid redirect URI
        if (empty(trim($redirectTo))) {
            Log::warning('Default OAuth client "' . self::DEFAULT_CLIENT_NAME . '" has no redirect URIs configured');
            return;
        }

        $fields = [
            'client_id' => $client->id,
            'redirect_uri' => trim($redirectTo),
            'scope' => '',
            'response_type' => 'code',
        ];

        request()->session()->put('authRequest', $fields);
    }

    /**
     * Returns the parameters for the redirect URL if the redirect URL is for registration.
     *
     * @return array|null
     */
    public function isRegister()
    {
        // Use the new validation method to ensure session data is valid
        if ($this->isValidSessionForCurrentRequest() && session('is_first_time')) {
            session()->put('is_first_time', false);

            $redirectUrl = Session::get('redirect_uri');
            $parsedUrl = parse_url((string) $redirectUrl);

            $params = [];
            parse_str(@$parsedUrl['query'], $params);
            if (isset($params['is_register']) && $params['is_register']) {
                $parameters = [];
                if (isset($params['email'])) {
                    $parameters['email'] = $params['email'];
                }

                return $parameters;
            }
        }

        // FIXED: Only return email parameters if this is explicitly a registration request
        // This prevents normal login URLs with email parameters from triggering registration flow
        if (request()->has('email') && request()->has('is_register') && request()->get('is_register') == '1') {
            return request()->only(['email']);
        }

        return false;
    }

    /**
     * Check if an email address already exists in the database.
     *
     * @param string $email
     * @return bool
     */
    public function emailExists(string $email): bool
    {
        return \App\Models\User::where('email', $email)->exists();
    }

    /**
     * Handle registration flow logic based on OAuth parameters.
     *
     * This method processes the is_register and email parameters from OAuth authorization URL
     * and determines the appropriate redirect action.
     *
     * @return array|false Returns array with redirect info or false if not a registration flow
     */
    public function handleRegistrationFlow()
    {
        $registrationParams = $this->extractRegistrationParams();

        if (!$registrationParams) {
            return false;
        }

        return $this->determineRegistrationAction($registrationParams['email']);
    }

    /**
     * Extract registration parameters from request or session.
     *
     * @return array|null
     */
    private function extractRegistrationParams(): ?array
    {
        // Check current request parameters first
        if (request()->has('is_register') && request()->get('is_register') == '1') {
            return ['email' => request()->get('email')];
        }

        // Check session-stored redirect URI only if we have the is_first_time flag
        // and the session data is valid for the current request context
        if ($this->isValidSessionForCurrentRequest()) {
            $redirectUrl = Session::get('redirect_uri');
            $parsedUrl = parse_url((string) $redirectUrl);

            $params = [];
            parse_str(@$parsedUrl['query'], $params);

            if (isset($params['is_register']) && $params['is_register'] == '1') {
                return ['email' => $params['email'] ?? null];
            }
        }

        return null;
    }

    /**
     * Determine the appropriate action based on email existence.
     *
     * @param string|null $email
     * @return array
     */
    private function determineRegistrationAction(?string $email): array
    {
        if (!$email) {
            return [
                'action' => 'register',
                'email' => null,
                'message' => 'Registration flow without email'
            ];
        }

        if ($this->emailExists($email)) {
            return [
                'action' => 'login',
                'email' => $email,
                'message' => 'Email already exists, redirecting to login'
            ];
        }

        return [
            'action' => 'register',
            'email' => $email,
            'message' => 'New email, redirecting to registration'
        ];
    }

    /**
     * Check if the given email address is a Gmail address.
     *
     * @param  string  $email
     * @return bool
     */
    public function isGmail(string $email): bool
    {
        return str_contains($email, '@gmail.com');
    }

    /**
     * Stores the redirect URI for the current request in the session.
     *
     * @return void
     */
    protected function setRedirectUri(): void
    {
        if (request()->session()->has('redirect_uri')) {
            return;
        }

        if (@request()->session()->get('url')['intended']) {
            request()->session()->put('redirect_uri', request()->session()->get('url')['intended']);
        }

        $previousUrl = request()->session()->previousUrl();
        $parsedUrl = parse_url((string) $previousUrl);

        $params = [];
        parse_str(@$parsedUrl['query'], $params);

        if (isset($params['redirect_uri'])) {
            $client = Client::where('id', $params['client_id'])
                ->whereRaw("FIND_IN_SET('{$params['redirect_uri']}', redirect) > 0")
                ->first();

            if ($client) {
                request()->session()->put('redirect_uri', $previousUrl);
            }
        }

        $this->setDefaultRedirection();
    }

    /**
     * Check if the current session data is valid for the current request context.
     *
     * This prevents session data from previous OAuth requests from interfering
     * with the current request, especially when switching between login and registration flows.
     *
     * @return bool
     */
    private function isValidSessionForCurrentRequest(): bool
    {
        // Check basic session requirements
        $hasBasicSession = session()->has('redirect_uri') && session()->has('is_first_time');

        // Check flow type compatibility
        $flowTypeMatches = $this->validateFlowTypeCompatibility();

        // Check OAuth parameters validity
        $hasValidOAuthParams = $this->validateOAuthParameters();

        return $hasBasicSession && $flowTypeMatches && $hasValidOAuthParams;
    }

    /**
     * Validate that the session flow type matches the current request.
     *
     * @return bool
     */
    private function validateFlowTypeCompatibility(): bool
    {
        if (!session()->has('oauth_flow_type')) {
            return true; // No flow type restriction
        }

        $sessionFlowType = session()->get('oauth_flow_type');
        $currentFlowType = request()->has('is_register') && request()->get('is_register') == '1' ? 'registration' : 'login';

        return $sessionFlowType === $currentFlowType;
    }

    /**
     * Validate that session contains valid OAuth parameters.
     *
     * @return bool
     */
    private function validateOAuthParameters(): bool
    {
        $redirectUrl = session()->get('redirect_uri');
        $parsedUrl = parse_url((string) $redirectUrl);

        if (!isset($parsedUrl['query'])) {
            return false;
        }

        $params = [];
        parse_str($parsedUrl['query'], $params);

        // Validate that session data has required OAuth parameters
        $requiredParams = ['client_id', 'redirect_uri', 'response_type'];
        foreach ($requiredParams as $param) {
            if (!isset($params[$param])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Clear session data that might interfere with OAuth flows.
     *
     * This method can be called to clean up session state when switching
     * between different OAuth flows or when starting a fresh flow.
     *
     * @return void
     */
    public function clearOAuthSessionData(): void
    {
        $keysToForget = [
            'redirect_uri',
            'authRequest',
            'is_first_time',
            'oauth_request_id',
            'oauth_flow_type'
        ];

        session()->forget($keysToForget);
    }

    /**
     * Validate that session data belongs to the current OAuth request.
     *
     * @param string|null $expectedClientId
     * @return bool
     */
    public function validateSessionForOAuthRequest(?string $expectedClientId = null): bool
    {
        if (!$this->isValidSessionForCurrentRequest()) {
            return false;
        }

        // If client ID is provided, validate it matches session data
        if ($expectedClientId !== null) {
            $redirectUrl = session()->get('redirect_uri');
            $parsedUrl = parse_url((string) $redirectUrl);

            $params = [];
            parse_str(@$parsedUrl['query'], $params);

            if (!isset($params['client_id']) || $params['client_id'] !== $expectedClientId) {
                return false;
            }
        }

        return true;
    }
}

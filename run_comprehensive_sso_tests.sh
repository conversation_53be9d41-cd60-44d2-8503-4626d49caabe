#!/bin/bash

# Comprehensive Laravel Passport SSO Integration Tests Runner
# This script runs all comprehensive SSO authentication flow tests

echo "🚀 Running Comprehensive Laravel Passport SSO Integration Tests"
echo "=============================================================="

# Change to the project directory
cd /Applications/XAMPP/xamppfiles/htdocs/gravitywrite/gravitywrite-auth

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}✅ $message: PASSED${NC}"
    elif [ "$status" = "FAIL" ]; then
        echo -e "${RED}❌ $message: FAILED${NC}"
    elif [ "$status" = "INFO" ]; then
        echo -e "${BLUE}ℹ️  $message${NC}"
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}⚠️  $message${NC}"
    fi
}

# Function to run test and check result
run_test() {
    local test_file=$1
    local test_name=$2
    
    print_status "INFO" "Running $test_name..."
    
    if php artisan test --env=testing "$test_file" --stop-on-failure; then
        print_status "PASS" "$test_name"
        return 0
    else
        print_status "FAIL" "$test_name"
        return 1
    fi
}

echo ""
print_status "INFO" "Test Categories:"
echo "1. Database Isolation Verification"
echo "2. Comprehensive SSO Authentication Flows"
echo "3. Session Management and State Handling"
echo "4. Client Redirect with is_register Parameter"
echo "5. Edge Cases and Security Scenarios"
echo ""

# Initialize test results
total_tests=0
passed_tests=0
failed_tests=0

# Test 1: Database Isolation Verification
echo "🔍 Testing Database Isolation..."
if run_test "tests/Feature/DatabaseIsolationTest.php" "Database Isolation Verification"; then
    ((passed_tests++))
else
    ((failed_tests++))
    echo "❌ Database isolation failed - stopping tests for safety"
    exit 1
fi
((total_tests++))

echo ""

# Test 2: Comprehensive SSO Authentication Flows
echo "🔐 Testing Comprehensive SSO Authentication Flows..."
if run_test "tests/Feature/Auth/PassportSsoComprehensiveTest.php" "Comprehensive SSO Authentication Flows"; then
    ((passed_tests++))
else
    ((failed_tests++))
fi
((total_tests++))

echo ""

# Test 3: Session Management and State Handling
echo "🗂️  Testing Session Management and State Handling..."
if run_test "tests/Feature/Auth/PassportSsoSessionManagementTest.php" "Session Management and State Handling"; then
    ((passed_tests++))
else
    ((failed_tests++))
fi
((total_tests++))

echo ""

# Test 4: Client Redirect with is_register Parameter
echo "🔄 Testing Client Redirect with is_register Parameter..."
if run_test "tests/Feature/Auth/PassportSsoClientRedirectTest.php" "Client Redirect with is_register Parameter"; then
    ((passed_tests++))
else
    ((failed_tests++))
fi
((total_tests++))

echo ""

# Test 5: Edge Cases and Security Scenarios
echo "🛡️  Testing Edge Cases and Security Scenarios..."
if run_test "tests/Feature/Auth/PassportSsoEdgeCasesTest.php" "Edge Cases and Security Scenarios"; then
    ((passed_tests++))
else
    ((failed_tests++))
fi
((total_tests++))

echo ""

# Test 6: Run existing OAuth integration tests for regression
echo "🔄 Running Existing OAuth Integration Tests (Regression)..."
if run_test "tests/Feature/Auth/OAuthIntegrationTest.php" "Existing OAuth Integration Tests"; then
    ((passed_tests++))
else
    ((failed_tests++))
fi
((total_tests++))

echo ""

# Test 7: Run existing SSO integration tests for regression
echo "🔄 Running Existing SSO Integration Tests (Regression)..."
if run_test "tests/Feature/Auth/SsoIntegrationTest.php" "Existing SSO Integration Tests"; then
    ((passed_tests++))
else
    ((failed_tests++))
fi
((total_tests++))

echo ""

# Test 8: Run existing SSO security tests for regression
echo "🛡️  Running Existing SSO Security Tests (Regression)..."
if run_test "tests/Feature/Auth/SsoSecurityTest.php" "Existing SSO Security Tests"; then
    ((passed_tests++))
else
    ((failed_tests++))
fi
((total_tests++))

echo ""
echo "=============================================================="
echo "📊 TEST SUMMARY"
echo "=============================================================="

if [ $failed_tests -eq 0 ]; then
    print_status "PASS" "All tests completed successfully!"
    echo -e "${GREEN}🎉 Total: $total_tests | Passed: $passed_tests | Failed: $failed_tests${NC}"
    echo ""
    echo "✅ Your Laravel Passport SSO authentication system is working correctly!"
    echo "✅ Database isolation is properly configured"
    echo "✅ All authentication flows are functioning as expected"
    echo "✅ Session management is working correctly"
    echo "✅ Security measures are in place"
else
    print_status "FAIL" "Some tests failed!"
    echo -e "${RED}💥 Total: $total_tests | Passed: $passed_tests | Failed: $failed_tests${NC}"
    echo ""
    echo "❌ Please review the failed tests and fix any issues"
    echo "❌ Check the test output above for specific error details"
fi

echo ""
echo "=============================================================="
echo "📝 NEXT STEPS"
echo "=============================================================="

if [ $failed_tests -eq 0 ]; then
    echo "✅ Your SSO system is ready for production!"
    echo "✅ Consider running these tests regularly in your CI/CD pipeline"
    echo "✅ Monitor authentication flows in production"
else
    echo "🔧 Fix the failing tests before deploying to production"
    echo "🔧 Review authentication logic and session management"
    echo "🔧 Check OAuth client configuration"
fi

echo ""
echo "To run individual test categories:"
echo "• Database Isolation: php artisan test --env=testing tests/Feature/DatabaseIsolationTest.php"
echo "• Comprehensive Flows: php artisan test --env=testing tests/Feature/Auth/PassportSsoComprehensiveTest.php"
echo "• Session Management: php artisan test --env=testing tests/Feature/Auth/PassportSsoSessionManagementTest.php"
echo "• Client Redirects: php artisan test --env=testing tests/Feature/Auth/PassportSsoClientRedirectTest.php"
echo "• Edge Cases: php artisan test --env=testing tests/Feature/Auth/PassportSsoEdgeCasesTest.php"

echo ""
echo "🏁 Test run completed at $(date)"

# Exit with appropriate code
if [ $failed_tests -eq 0 ]; then
    exit 0
else
    exit 1
fi
